#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت تثبيت المتطلبات
Requirements Installation Script

يقوم بتثبيت جميع المكتبات المطلوبة لتشغيل البرنامج
مع التعامل مع الأخطاء والتحقق من التثبيت

المطور: مساعد الذكي الاصطناعي
التاريخ: 2025-06-21
"""

import subprocess
import sys
import os

def install_package(package):
    """تثبيت مكتبة واحدة"""
    try:
        print(f"جاري تثبيت {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ تم تثبيت {package} بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت {package}: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء تثبيت متطلبات برنامج ضغط وسكر")
    print("=" * 50)
    
    # قائمة المكتبات المطلوبة
    required_packages = [
        "pandas==2.1.4",
        "numpy==1.24.3", 
        "matplotlib==3.8.2",
        "seaborn==0.13.0",
        "reportlab==4.0.7",
        "openpyxl==3.1.2",
        "xlsxwriter==3.1.9",
        "python-dateutil==2.8.2",
        "pillow==10.1.0",
        "plyer==2.1.0",
        "cryptography==41.0.8",
        "arabic-reshaper==3.0.0",
        "python-bidi==0.4.2"
    ]
    
    # المكتبات الاختيارية (لتحسين الواجهة)
    optional_packages = [
        "customtkinter==5.2.2",
        "ttkthemes==3.2.2"
    ]
    
    success_count = 0
    failed_packages = []
    
    # تثبيت المكتبات المطلوبة
    print("📦 تثبيت المكتبات الأساسية:")
    for package in required_packages:
        if install_package(package):
            success_count += 1
        else:
            failed_packages.append(package)
    
    print("\n📦 تثبيت المكتبات الاختيارية:")
    for package in optional_packages:
        if install_package(package):
            success_count += 1
        else:
            print(f"⚠️  {package} اختياري - يمكن تشغيل البرنامج بدونه")
    
    print("\n" + "=" * 50)
    print(f"📊 النتائج:")
    print(f"✅ تم تثبيت {success_count} مكتبة بنجاح")
    
    if failed_packages:
        print(f"❌ فشل في تثبيت {len(failed_packages)} مكتبة:")
        for package in failed_packages:
            print(f"   - {package}")
        print("\n⚠️  قد تحتاج إلى تثبيت هذه المكتبات يدوياً")
    else:
        print("🎉 تم تثبيت جميع المكتبات بنجاح!")
    
    print("\n🔧 للتشغيل:")
    print("   python main.py")
    
    print("\n📝 ملاحظات:")
    print("   - تأكد من أن لديك Python 3.8 أو أحدث")
    print("   - قد تحتاج إلى تشغيل الأمر كمدير (Administrator)")
    print("   - في حالة وجود مشاكل، جرب: pip install --upgrade pip")

if __name__ == "__main__":
    main()
