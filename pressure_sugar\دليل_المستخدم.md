# دليل المستخدم - برنامج ضغط وسكر

## 🎯 مرحباً بك في برنامج ضغط وسكر!

هذا البرنامج مصمم لمساعدتك في تتبع ومراقبة قياسات ضغط الدم ومستوى السكر بطريقة سهلة ومنظمة.

## 🚀 التشغيل السريع

### على Windows:
1. انقر نقراً مزدوجاً على `run_app.bat`
2. أو افتح Command Prompt واكتب: `python main.py`

### على Linux/Mac:
1. افتح Terminal واكتب: `./run_app.sh`
2. أو اكتب: `python3 main.py`

## 📋 الخطوات الأولى

### 1. إضافة مستخدم جديد
- اضغط على زر "مستخدم جديد" في شريط الأدوات
- أدخل المعلومات المطلوبة:
  - **الاسم** (مطلوب)
  - العمر (اختياري)
  - الجنس (اختياري)
  - الحالات الطبية (اختياري)
  - الأدوية الحالية (اختياري)
- اضغط "حفظ"

### 2. تسجيل قياس جديد
- اضغط على زر "قياس جديد"
- أدخل البيانات:
  - **التاريخ والوقت** (يتم ملؤهما تلقائياً)
  - **ضغط الدم**: الانقباضي والانبساطي
  - **مستوى السكر**: القيمة ونوع القياس (صائم/بعد الأكل)
  - **ملاحظات** (اختياري)
- اضغط "حفظ"

## 📊 فهم واجهة البرنامج

### الصفحة الرئيسية
- **إحصائيات سريعة**: عدد قياسات اليوم وآخر القراءات
- **التنبيهات الأخيرة**: تحذيرات للقراءات غير الطبيعية

### تبويب القياسات
- جدول يعرض جميع القياسات المسجلة
- مرتبة حسب التاريخ (الأحدث أولاً)
- يمكن التمرير لرؤية القياسات القديمة

### تبويب الإحصائيات
- رسوم بيانية لتطور القياسات
- إحصائيات مفصلة للفترات المختلفة

## ⚠️ فهم التنبيهات

### ضغط الدم:
- **🟢 طبيعي**: أقل من 120/80
- **🟡 مرتفع قليلاً**: 120-129/أقل من 80
- **🟠 مرتفع المرحلة 1**: 130-139/80-89
- **🔴 مرتفع المرحلة 2**: 140/90 أو أعلى

### مستوى السكر:
- **🟢 صائم طبيعي**: 70-100 mg/dL
- **🟢 بعد الأكل طبيعي**: أقل من 140 mg/dL
- **🟡 مرتفع قليلاً**: 100-125 (صائم) أو 140-199 (بعد الأكل)
- **🔴 مرتفع**: أعلى من 126 (صائم) أو 200 (بعد الأكل)
- **🔴 منخفض**: أقل من 70 mg/dL

## 📄 التقارير والتصدير

### إنشاء تقرير:
1. اضغط على "التقارير"
2. اختر الفترة الزمنية
3. اختر نوع التقرير (PDF أو Excel)
4. اضغط "تصدير"

### محتويات التقرير:
- معلومات المستخدم
- جدول القياسات
- الإحصائيات والمتوسطات
- الرسوم البيانية
- التوصيات الصحية

## 🔧 الإعدادات

### تخصيص التنبيهات:
- تفعيل/إلغاء الإشعارات
- تعديل الحدود الطبيعية
- تخصيص أوقات التذكير

### النسخ الاحتياطي:
- البيانات محفوظة في مجلد `data`
- يمكن نسخ هذا المجلد للاحتفاظ بنسخة احتياطية
- التقارير محفوظة في مجلد `reports`

## 💡 نصائح مهمة

### لقياسات دقيقة:
1. **ضغط الدم**:
   - اجلس مسترخياً لمدة 5 دقائق قبل القياس
   - ضع الذراع على مستوى القلب
   - لا تتحدث أثناء القياس
   - خذ قياسين بفاصل دقيقة واحدة

2. **مستوى السكر**:
   - اغسل يديك قبل القياس
   - استخدم جانب الإصبع وليس الطرف
   - سجل نوع القياس بدقة (صائم/بعد الأكل)

### للمتابعة الطبية:
- اطبع التقارير الشهرية لطبيبك
- سجل أي أعراض في خانة الملاحظات
- لا تتجاهل التنبيهات الحمراء

## 🆘 حل المشاكل الشائعة

### البرنامج لا يبدأ:
1. تأكد من تثبيت Python 3.8 أو أحدث
2. شغل: `python install_requirements.py`
3. تأكد من وجود جميع الملفات

### خطأ في قاعدة البيانات:
1. تأكد من صلاحيات الكتابة في مجلد البرنامج
2. أغلق البرنامج وأعد تشغيله
3. تحقق من مساحة القرص الصلب

### مشاكل الخطوط العربية:
1. تأكد من تثبيت خطوط عربية على النظام
2. جرب تشغيل البرنامج كمدير (Administrator)

### التنبيهات لا تعمل:
1. تحقق من إعدادات التنبيهات
2. تأكد من تفعيل الإشعارات في النظام

## 📞 الدعم والمساعدة

### ملفات السجلات:
- تحقق من `data/app.log` لرسائل الأخطاء
- هذا الملف يساعد في تشخيص المشاكل

### النسخ الاحتياطي:
- انسخ مجلد `data` بانتظام
- احتفظ بنسخة من التقارير المهمة

## 🔒 الخصوصية والأمان

- جميع البيانات محفوظة محلياً على جهازك
- لا يتم إرسال أي معلومات عبر الإنترنت
- يمكنك حذف البيانات في أي وقت

## 📈 نصائح للاستفادة القصوى

1. **سجل القياسات يومياً** في نفس الأوقات
2. **استخدم الملاحظات** لتسجيل الأعراض أو الأنشطة
3. **راجع التقارير الشهرية** مع طبيبك
4. **لا تتجاهل التنبيهات** الحمراء
5. **احتفظ بنسخ احتياطية** من بياناتك

---

**تذكر**: هذا البرنامج أداة مساعدة وليس بديلاً عن الاستشارة الطبية. استشر طبيبك دائماً حول حالتك الصحية.
