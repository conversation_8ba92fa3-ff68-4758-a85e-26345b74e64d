#!/bin/bash

# برنامج ضغط وسكر - حفظ القياسات وتحليلها
# Blood Pressure & Sugar Tracker

echo ""
echo "========================================"
echo "   برنامج ضغط وسكر - حفظ القياسات وتحليلها"
echo "   Blood Pressure & Sugar Tracker"
echo "========================================"
echo ""

# التحقق من وجود Python
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "❌ Python غير مثبت على النظام"
        echo "يرجى تثبيت Python 3.8 أو أحدث"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo "✅ تم العثور على Python"
$PYTHON_CMD --version

# التحقق من وجود المتطلبات
echo ""
echo "🔍 فحص المتطلبات..."

$PYTHON_CMD -c "import pandas, matplotlib, reportlab" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "⚠️  بعض المكتبات المطلوبة غير مثبتة"
    echo ""
    read -p "هل تريد تثبيت المتطلبات الآن؟ (y/n): " install_deps
    if [[ $install_deps == "y" || $install_deps == "Y" ]]; then
        echo ""
        echo "📦 جاري تثبيت المتطلبات..."
        $PYTHON_CMD install_requirements.py
        echo ""
        read -p "اضغط Enter للمتابعة..."
    else
        echo "يمكنك تثبيت المتطلبات لاحقاً بتشغيل: $PYTHON_CMD install_requirements.py"
        echo ""
    fi
fi

echo ""
echo "🚀 بدء تشغيل البرنامج..."
echo ""

# تشغيل البرنامج
$PYTHON_CMD main.py

# في حالة إغلاق البرنامج
echo ""
echo "👋 تم إغلاق البرنامج"
read -p "اضغط Enter للخروج..."
