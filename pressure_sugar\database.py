#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة إدارة قاعدة البيانات
Database Management Module

تحتوي على فئات وطرق إدارة قاعدة البيانات SQLite
لحفظ قياسات ضغط الدم والسكر

المطور: مساعد الذكي الاصطناعي
التاريخ: 2025-06-21
"""

import sqlite3
import os
import logging
import threading
from datetime import datetime, date
from typing import List, Dict, Optional, Tuple
import json

logger = logging.getLogger(__name__)

class DatabaseManager:
    """مدير قاعدة البيانات الرئيسي"""
    
    def __init__(self, db_path: str = "data/measurements.db"):
        """
        تهيئة مدير قاعدة البيانات

        Args:
            db_path: مسار ملف قاعدة البيانات
        """
        self.db_path = db_path
        self.connection = None
        self.cursor = None
        self.lock = threading.Lock()  # قفل للحماية من الخيوط المتعددة

        # إنشاء مجلد البيانات إذا لم يكن موجوداً
        os.makedirs(os.path.dirname(db_path), exist_ok=True)

        # الاتصال بقاعدة البيانات وإنشاء الجداول
        self.connect()
        self.create_tables()
    
    def connect(self):
        """الاتصال بقاعدة البيانات"""
        try:
            self.connection = sqlite3.connect(
                self.db_path,
                detect_types=sqlite3.PARSE_DECLTYPES | sqlite3.PARSE_COLNAMES,
                check_same_thread=False  # السماح بالاستخدام من خيوط متعددة
            )
            self.connection.row_factory = sqlite3.Row
            self.cursor = self.connection.cursor()
            logger.info(f"تم الاتصال بقاعدة البيانات: {self.db_path}")

        except sqlite3.Error as e:
            logger.error(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            raise
    
    def create_tables(self):
        """إنشاء جداول قاعدة البيانات"""
        try:
            # جدول المستخدمين
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    age INTEGER,
                    gender TEXT,
                    medical_conditions TEXT,
                    medications TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # جدول القياسات
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS measurements (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    measurement_date DATE NOT NULL,
                    measurement_time TIME NOT NULL,
                    systolic_pressure INTEGER,
                    diastolic_pressure INTEGER,
                    blood_sugar REAL,
                    sugar_type TEXT DEFAULT 'fasting',
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')
            
            # جدول الأدوية والجرعات
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS medications (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    medication_name TEXT NOT NULL,
                    dosage TEXT,
                    frequency TEXT,
                    start_date DATE,
                    end_date DATE,
                    notes TEXT,
                    active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')
            
            # جدول التنبيهات
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS alerts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    alert_type TEXT NOT NULL,
                    message TEXT NOT NULL,
                    alert_date DATE NOT NULL,
                    alert_time TIME NOT NULL,
                    is_read BOOLEAN DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')
            
            # جدول الإعدادات
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS settings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    setting_key TEXT UNIQUE NOT NULL,
                    setting_value TEXT,
                    description TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            self.connection.commit()
            logger.info("تم إنشاء جداول قاعدة البيانات بنجاح")
            
            # إدراج الإعدادات الافتراضية
            self._insert_default_settings()
            
        except sqlite3.Error as e:
            logger.error(f"خطأ في إنشاء الجداول: {e}")
            raise
    
    def _insert_default_settings(self):
        """إدراج الإعدادات الافتراضية"""
        default_settings = [
            ('normal_systolic_max', '120', 'الحد الأقصى للضغط الانقباضي الطبيعي'),
            ('normal_diastolic_max', '80', 'الحد الأقصى للضغط الانبساطي الطبيعي'),
            ('high_systolic_min', '140', 'الحد الأدنى للضغط الانقباضي المرتفع'),
            ('high_diastolic_min', '90', 'الحد الأدنى للضغط الانبساطي المرتفع'),
            ('normal_sugar_fasting_max', '100', 'الحد الأقصى للسكر الصائم الطبيعي'),
            ('normal_sugar_after_meal_max', '140', 'الحد الأقصى للسكر بعد الأكل الطبيعي'),
            ('high_sugar_min', '180', 'الحد الأدنى للسكر المرتفع'),
            ('app_language', 'ar', 'لغة التطبيق'),
            ('backup_enabled', '1', 'تفعيل النسخ الاحتياطي'),
            ('notifications_enabled', '1', 'تفعيل الإشعارات')
        ]
        
        for key, value, desc in default_settings:
            self.cursor.execute('''
                INSERT OR IGNORE INTO settings (setting_key, setting_value, description)
                VALUES (?, ?, ?)
            ''', (key, value, desc))
        
        self.connection.commit()
    
    def close(self):
        """إغلاق الاتصال بقاعدة البيانات"""
        if self.connection:
            self.connection.close()
            logger.info("تم إغلاق الاتصال بقاعدة البيانات")
    
    def add_user(self, name: str, age: int = None, gender: str = None, 
                 medical_conditions: str = None, medications: str = None) -> int:
        """
        إضافة مستخدم جديد
        
        Args:
            name: اسم المستخدم
            age: العمر
            gender: الجنس
            medical_conditions: الحالات الطبية
            medications: الأدوية
            
        Returns:
            معرف المستخدم الجديد
        """
        try:
            self.cursor.execute('''
                INSERT INTO users (name, age, gender, medical_conditions, medications)
                VALUES (?, ?, ?, ?, ?)
            ''', (name, age, gender, medical_conditions, medications))
            
            self.connection.commit()
            user_id = self.cursor.lastrowid
            logger.info(f"تم إضافة مستخدم جديد: {name} (ID: {user_id})")
            return user_id
            
        except sqlite3.Error as e:
            logger.error(f"خطأ في إضافة المستخدم: {e}")
            raise
    
    def get_users(self) -> List[Dict]:
        """الحصول على قائمة جميع المستخدمين"""
        with self.lock:
            try:
                self.cursor.execute('SELECT * FROM users ORDER BY name')
                users = [dict(row) for row in self.cursor.fetchall()]
                return users

            except sqlite3.Error as e:
                logger.error(f"خطأ في جلب المستخدمين: {e}")
                return []

    def add_measurement(self, user_id: int, measurement_date: date,
                       measurement_time: str, systolic_pressure: int = None,
                       diastolic_pressure: int = None, blood_sugar: float = None,
                       sugar_type: str = 'fasting', notes: str = None) -> int:
        """
        إضافة قياس جديد

        Args:
            user_id: معرف المستخدم
            measurement_date: تاريخ القياس
            measurement_time: وقت القياس
            systolic_pressure: الضغط الانقباضي
            diastolic_pressure: الضغط الانبساطي
            blood_sugar: مستوى السكر
            sugar_type: نوع قياس السكر (fasting/after_meal)
            notes: ملاحظات

        Returns:
            معرف القياس الجديد
        """
        try:
            self.cursor.execute('''
                INSERT INTO measurements
                (user_id, measurement_date, measurement_time, systolic_pressure,
                 diastolic_pressure, blood_sugar, sugar_type, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (user_id, measurement_date, measurement_time, systolic_pressure,
                  diastolic_pressure, blood_sugar, sugar_type, notes))

            self.connection.commit()
            measurement_id = self.cursor.lastrowid
            logger.info(f"تم إضافة قياس جديد (ID: {measurement_id})")

            # فحص القراءات وإنشاء تنبيهات إذا لزم الأمر
            self._check_and_create_alerts(user_id, systolic_pressure,
                                        diastolic_pressure, blood_sugar, sugar_type)

            return measurement_id

        except sqlite3.Error as e:
            logger.error(f"خطأ في إضافة القياس: {e}")
            raise

    def get_measurements(self, user_id: int = None, start_date: date = None,
                        end_date: date = None, limit: int = None) -> List[Dict]:
        """
        الحصول على القياسات

        Args:
            user_id: معرف المستخدم (اختياري)
            start_date: تاريخ البداية (اختياري)
            end_date: تاريخ النهاية (اختياري)
            limit: عدد النتائج المحدود (اختياري)

        Returns:
            قائمة القياسات
        """
        with self.lock:
            try:
                query = '''
                    SELECT m.*, u.name as user_name
                    FROM measurements m
                    LEFT JOIN users u ON m.user_id = u.id
                    WHERE 1=1
                '''
                params = []

                if user_id:
                    query += ' AND m.user_id = ?'
                    params.append(user_id)

                if start_date:
                    query += ' AND m.measurement_date >= ?'
                    params.append(start_date)

                if end_date:
                    query += ' AND m.measurement_date <= ?'
                    params.append(end_date)

                query += ' ORDER BY m.measurement_date DESC, m.measurement_time DESC'

                if limit:
                    query += ' LIMIT ?'
                    params.append(limit)

                self.cursor.execute(query, params)
                measurements = [dict(row) for row in self.cursor.fetchall()]
                return measurements

            except sqlite3.Error as e:
                logger.error(f"خطأ في جلب القياسات: {e}")
                return []

    def _check_and_create_alerts(self, user_id: int, systolic: int = None,
                               diastolic: int = None, blood_sugar: float = None,
                               sugar_type: str = 'fasting'):
        """فحص القراءات وإنشاء تنبيهات للقراءات الخطيرة"""
        try:
            alerts = []
            current_date = date.today()
            current_time = datetime.now().strftime('%H:%M')

            # فحص ضغط الدم
            if systolic and diastolic:
                if systolic >= 140 or diastolic >= 90:
                    alerts.append({
                        'type': 'high_blood_pressure',
                        'message': f'تحذير: ضغط الدم مرتفع ({systolic}/{diastolic})'
                    })
                elif systolic < 90 or diastolic < 60:
                    alerts.append({
                        'type': 'low_blood_pressure',
                        'message': f'تحذير: ضغط الدم منخفض ({systolic}/{diastolic})'
                    })

            # فحص السكر
            if blood_sugar:
                if sugar_type == 'fasting' and blood_sugar > 126:
                    alerts.append({
                        'type': 'high_blood_sugar',
                        'message': f'تحذير: السكر الصائم مرتفع ({blood_sugar} mg/dL)'
                    })
                elif sugar_type == 'after_meal' and blood_sugar > 200:
                    alerts.append({
                        'type': 'high_blood_sugar',
                        'message': f'تحذير: السكر بعد الأكل مرتفع ({blood_sugar} mg/dL)'
                    })
                elif blood_sugar < 70:
                    alerts.append({
                        'type': 'low_blood_sugar',
                        'message': f'تحذير: السكر منخفض ({blood_sugar} mg/dL)'
                    })

            # إدراج التنبيهات في قاعدة البيانات
            for alert in alerts:
                self.cursor.execute('''
                    INSERT INTO alerts (user_id, alert_type, message, alert_date, alert_time)
                    VALUES (?, ?, ?, ?, ?)
                ''', (user_id, alert['type'], alert['message'], current_date, current_time))

            if alerts:
                self.connection.commit()
                logger.info(f"تم إنشاء {len(alerts)} تنبيه جديد")

        except sqlite3.Error as e:
            logger.error(f"خطأ في إنشاء التنبيهات: {e}")

    def get_alerts(self, user_id: int = None, unread_only: bool = False) -> List[Dict]:
        """الحصول على التنبيهات"""
        with self.lock:
            try:
                query = '''
                    SELECT a.*, u.name as user_name
                    FROM alerts a
                    LEFT JOIN users u ON a.user_id = u.id
                    WHERE 1=1
                '''
                params = []

                if user_id:
                    query += ' AND a.user_id = ?'
                    params.append(user_id)

                if unread_only:
                    query += ' AND a.is_read = 0'

                query += ' ORDER BY a.alert_date DESC, a.alert_time DESC'

                self.cursor.execute(query, params)
                alerts = [dict(row) for row in self.cursor.fetchall()]
                return alerts

            except sqlite3.Error as e:
                logger.error(f"خطأ في جلب التنبيهات: {e}")
                return []

    def mark_alert_as_read(self, alert_id: int):
        """تحديد التنبيه كمقروء"""
        try:
            self.cursor.execute('''
                UPDATE alerts SET is_read = 1 WHERE id = ?
            ''', (alert_id,))
            self.connection.commit()

        except sqlite3.Error as e:
            logger.error(f"خطأ في تحديث التنبيه: {e}")

    def get_setting(self, key: str) -> str:
        """الحصول على قيمة إعداد"""
        try:
            self.cursor.execute('''
                SELECT setting_value FROM settings WHERE setting_key = ?
            ''', (key,))
            result = self.cursor.fetchone()
            return result['setting_value'] if result else None

        except sqlite3.Error as e:
            logger.error(f"خطأ في جلب الإعداد: {e}")
            return None

    def set_setting(self, key: str, value: str):
        """تحديث قيمة إعداد"""
        try:
            self.cursor.execute('''
                UPDATE settings SET setting_value = ?, updated_at = CURRENT_TIMESTAMP
                WHERE setting_key = ?
            ''', (value, key))
            self.connection.commit()

        except sqlite3.Error as e:
            logger.error(f"خطأ في تحديث الإعداد: {e}")

    def get_statistics(self, user_id: int, days: int = 30) -> Dict:
        """الحصول على إحصائيات المستخدم"""
        try:
            from datetime import timedelta
            start_date = date.today() - timedelta(days=days)

            # إحصائيات ضغط الدم
            self.cursor.execute('''
                SELECT
                    COUNT(*) as total_measurements,
                    AVG(systolic_pressure) as avg_systolic,
                    AVG(diastolic_pressure) as avg_diastolic,
                    MAX(systolic_pressure) as max_systolic,
                    MIN(systolic_pressure) as min_systolic,
                    MAX(diastolic_pressure) as max_diastolic,
                    MIN(diastolic_pressure) as min_diastolic
                FROM measurements
                WHERE user_id = ? AND measurement_date >= ?
                AND systolic_pressure IS NOT NULL AND diastolic_pressure IS NOT NULL
            ''', (user_id, start_date))

            bp_stats = dict(self.cursor.fetchone() or {})

            # إحصائيات السكر
            self.cursor.execute('''
                SELECT
                    COUNT(*) as total_sugar_measurements,
                    AVG(blood_sugar) as avg_blood_sugar,
                    MAX(blood_sugar) as max_blood_sugar,
                    MIN(blood_sugar) as min_blood_sugar
                FROM measurements
                WHERE user_id = ? AND measurement_date >= ?
                AND blood_sugar IS NOT NULL
            ''', (user_id, start_date))

            sugar_stats = dict(self.cursor.fetchone() or {})

            # دمج الإحصائيات
            stats = {**bp_stats, **sugar_stats}
            stats['period_days'] = days

            return stats

        except sqlite3.Error as e:
            logger.error(f"خطأ في جلب الإحصائيات: {e}")
            return {}
