#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج ضغط وسكر - حفظ القياسات وتحليلها
Blood Pressure & Sugar Tracker

الملف الرئيسي للبرنامج
Main Application File

المطور: مساعد الذكي الاصطناعي
Developer: AI Assistant
التاريخ: 2025-06-21
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import logging

# إضافة مسار المشروع إلى sys.path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# استيراد الوحدات المحلية
try:
    from database import DatabaseManager
    from gui import MainApplication
    from analysis import DataAnalyzer
    from export import ReportExporter
    from notifications import NotificationManager
except ImportError as e:
    print(f"خطأ في استيراد الوحدات: {e}")
    print("تأكد من وجود جميع الملفات المطلوبة")
    sys.exit(1)

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('data/app.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class PressureSugarApp:
    """الفئة الرئيسية للتطبيق"""
    
    def __init__(self):
        """تهيئة التطبيق"""
        self.db_manager = None
        self.main_app = None
        self.data_analyzer = None
        self.report_exporter = None
        self.notification_manager = None
        
    def initialize_components(self):
        """تهيئة مكونات التطبيق"""
        try:
            # تهيئة قاعدة البيانات
            self.db_manager = DatabaseManager()
            logger.info("تم تهيئة قاعدة البيانات بنجاح")
            
            # تهيئة محلل البيانات
            self.data_analyzer = DataAnalyzer(self.db_manager)
            logger.info("تم تهيئة محلل البيانات بنجاح")
            
            # تهيئة مصدر التقارير
            self.report_exporter = ReportExporter(self.db_manager)
            logger.info("تم تهيئة مصدر التقارير بنجاح")

            # تهيئة مدير التنبيهات
            self.notification_manager = NotificationManager(self.db_manager)
            logger.info("تم تهيئة مدير التنبيهات بنجاح")

            return True
            
        except Exception as e:
            logger.error(f"خطأ في تهيئة المكونات: {e}")
            messagebox.showerror("خطأ", f"فشل في تهيئة التطبيق:\n{e}")
            return False
    
    def run(self):
        """تشغيل التطبيق"""
        try:
            # تهيئة المكونات
            if not self.initialize_components():
                return
            
            # إنشاء واجهة المستخدم الرئيسية
            root = tk.Tk()

            # تحديث مدير التنبيهات بالنافذة الرئيسية
            self.notification_manager.root_window = root

            self.main_app = MainApplication(
                root,
                self.db_manager,
                self.data_analyzer,
                self.report_exporter,
                self.notification_manager
            )

            # بدء مراقبة التنبيهات
            self.notification_manager.start_monitoring()
            
            logger.info("تم بدء تشغيل التطبيق")
            
            # تشغيل حلقة الأحداث الرئيسية
            root.mainloop()
            
        except Exception as e:
            logger.error(f"خطأ في تشغيل التطبيق: {e}")
            messagebox.showerror("خطأ", f"فشل في تشغيل التطبيق:\n{e}")
        
        finally:
            # تنظيف الموارد
            if self.notification_manager:
                self.notification_manager.stop_monitoring()
            if self.db_manager:
                self.db_manager.close()
            logger.info("تم إغلاق التطبيق")

def main():
    """الدالة الرئيسية"""
    try:
        # إنشاء مجلدات البيانات إذا لم تكن موجودة
        os.makedirs('data', exist_ok=True)
        os.makedirs('reports', exist_ok=True)
        os.makedirs('assets', exist_ok=True)
        
        # إنشاء وتشغيل التطبيق
        app = PressureSugarApp()
        app.run()
        
    except KeyboardInterrupt:
        logger.info("تم إيقاف التطبيق بواسطة المستخدم")
    except Exception as e:
        logger.error(f"خطأ غير متوقع: {e}")
        messagebox.showerror("خطأ", f"خطأ غير متوقع:\n{e}")

if __name__ == "__main__":
    main()
