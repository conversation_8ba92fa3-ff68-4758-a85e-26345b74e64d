# برنامج ضغط وسكر - حفظ القياسات وتحليلها
# Blood Pressure & Sugar Tracker

## 📋 وصف البرنامج
برنامج طبي بسيط يساعد المرضى والعيادات في تسجيل ومتابعة قراءات ضغط الدم ومستوى السكر في الدم بشكل دوري، مع توفير تحليلات وتنبيهات.

## ✨ المميزات الرئيسية
- 📊 تسجيل قياسات ضغط الدم والسكر يومياً
- 📈 رسوم بيانية لتتبع التغيرات بمرور الوقت
- 🔔 تنبيهات للقراءات الخطيرة
- 📄 تقارير قابلة للطباعة والتصدير (PDF/Excel)
- 🌐 واجهة مستخدم باللغة العربية
- 💾 حفظ آمن للبيانات في قاعدة بيانات محلية
- 📊 إحصائيات وتحليلات متقدمة

## 🛠️ المتطلبات التقنية
- Python 3.8 أو أحدث
- نظام التشغيل: Windows/Linux/macOS
- ذاكرة: 512 MB RAM على الأقل
- مساحة القرص: 100 MB

## 📦 التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd pressure_sugar
```

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 3. تشغيل البرنامج
```bash
python main.py
```

## 📁 هيكل المشروع
```
pressure_sugar/
│
├── main.py              # الملف الرئيسي
├── database.py          # إدارة قاعدة البيانات
├── gui.py              # واجهة المستخدم
├── analysis.py         # تحليل البيانات
├── export.py           # تصدير التقارير
├── requirements.txt    # المتطلبات
├── README.md          # هذا الملف
│
├── data/              # قاعدة البيانات والسجلات
├── reports/           # التقارير المُصدرة
└── assets/           # الصور والخطوط
```

## 🎯 كيفية الاستخدام

### تسجيل قياس جديد
1. اضغط على "قياس جديد"
2. أدخل قراءة ضغط الدم (انقباضي/انبساطي)
3. أدخل قراءة السكر
4. أضف أي ملاحظات
5. اضغط "حفظ"

### عرض التقارير
1. اختر "التقارير" من القائمة الرئيسية
2. حدد الفترة الزمنية
3. اختر نوع التقرير (يومي/أسبوعي/شهري)
4. اضغط "عرض" أو "تصدير"

### الرسوم البيانية
1. اختر "الرسوم البيانية"
2. حدد نوع البيانات (ضغط/سكر)
3. اختر الفترة الزمنية
4. شاهد التغيرات بمرور الوقت

## ⚠️ القيم الطبيعية والتنبيهات

### ضغط الدم
- طبيعي: أقل من 120/80
- مرتفع قليلاً: 120-129/أقل من 80
- مرتفع المرحلة 1: 130-139/80-89
- مرتفع المرحلة 2: 140/90 أو أعلى

### السكر
- صائم طبيعي: 70-100 mg/dL
- بعد الأكل طبيعي: أقل من 140 mg/dL
- مرتفع: أعلى من 180 mg/dL

## 🔒 الأمان والخصوصية
- جميع البيانات محفوظة محلياً على جهازك
- لا يتم إرسال أي بيانات عبر الإنترنت
- إمكانية عمل نسخ احتياطية

## 🆘 الدعم والمساعدة
- تحقق من ملف السجلات في `data/app.log`
- تأكد من تثبيت جميع المتطلبات
- راجع الأخطاء الشائعة أدناه

## 🐛 الأخطاء الشائعة وحلولها
1. **خطأ في استيراد الوحدات**: تأكد من تثبيت requirements.txt
2. **خطأ في قاعدة البيانات**: تحقق من صلاحيات الكتابة في مجلد data
3. **مشاكل الخطوط العربية**: تأكد من تثبيت الخطوط المطلوبة

## 📝 الترخيص
هذا البرنامج مجاني للاستخدام الشخصي والتعليمي.

## 👨‍💻 المطور
تم تطوير هذا البرنامج بواسطة مساعد الذكي الاصطناعي
التاريخ: 2025-06-21
