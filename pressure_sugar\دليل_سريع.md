# دليل سريع - برنامج ضغط وسكر

## 🚀 البدء السريع

### 1. تشغيل البرنامج
- **Windows**: انقر نقراً مزدوجاً على `run_app.bat`
- **Linux/Mac**: شغل `./run_app.sh` في Terminal
- **يدوياً**: `python main.py`

### 2. إضافة مستخدم جديد
1. اضغط "مستخدم جديد" في شريط الأدوات
2. أدخل الاسم (مطلوب) والمعلومات الأخرى
3. اضغط "حفظ"

### 3. تسجيل قياس
1. اختر المستخدم من القائمة العلوية
2. اضغط "قياس جديد"
3. أدخل قياسات ضغط الدم و/أو السكر
4. اضغط "حفظ"

## 📊 الميزات الجديدة

### الإعدادات المتقدمة
- **الوصول**: اضغط "الإعدادات" في شريط الأدوات
- **التنبيهات**: تخصيص أنواع التنبيهات والأصوات
- **الحدود الطبيعية**: تعديل القيم المرجعية للضغط والسكر
- **المظهر**: تغيير الألوان وحجم الخط
- **النسخ الاحتياطي**: إعداد النسخ التلقائي

### التقارير التفاعلية
- **الوصول**: اضغط "التقارير" في شريط الأدوات
- **إنشاء تقرير جديد**:
  - اختر المستخدم والفترة الزمنية
  - حدد نوع التقرير (شامل/ضغط/سكر/إحصائيات)
  - اختر تنسيق التصدير (PDF/Excel/كلاهما)
- **التقارير المحفوظة**: عرض وإدارة التقارير السابقة

### الرسوم البيانية التفاعلية
- **الوصول**: اضغط "الرسوم البيانية" في شريط الأدوات
- **أنواع الرسوم**:
  - ضغط الدم: رسم تطور الضغط الانقباضي والانبساطي
  - السكر: رسم مستويات السكر (صائم/بعد الأكل)
  - مدمج: عرض الضغط والسكر معاً
  - إحصائيات: رسوم دائرية وأعمدة للتوزيعات
- **خيارات التخصيص**:
  - فترات زمنية مختلفة (أسبوع/شهر/3 أشهر/مخصص)
  - خطوط الاتجاه والنطاقات الطبيعية
  - أنماط رسم مختلفة
- **الحفظ**: إمكانية حفظ الرسوم بصيغ مختلفة

## 🎯 نصائح سريعة

### للاستخدام الأمثل:
1. **سجل القياسات يومياً** في نفس الأوقات
2. **استخدم قائمة المستخدمين** في الأعلى للتنقل السريع
3. **راجع التنبيهات** في الصفحة الرئيسية بانتظام
4. **اطبع التقارير الشهرية** لطبيبك

### لحل المشاكل:
1. **إعادة تشغيل البرنامج** يحل معظم المشاكل
2. **تحقق من ملف السجلات** في `data/app.log`
3. **استخدم النسخ الاحتياطي** من الإعدادات

## 📱 اختصارات لوحة المفاتيح

- **Enter**: حفظ في النوافذ
- **Escape**: إلغاء/إغلاق النوافذ
- **F5**: تحديث البيانات (في النوافذ المدعومة)

## 🔧 الإعدادات المهمة

### الحدود الطبيعية الافتراضية:
- **ضغط الدم**: 120/80 (طبيعي)، 140/90 (مرتفع)
- **السكر**: 100 (صائم)، 140 (بعد الأكل)، 70 (منخفض)

### تنسيقات التاريخ:
- **التاريخ**: YYYY-MM-DD (مثل: 2025-06-21)
- **الوقت**: HH:MM (مثل: 14:30)

## 📞 المساعدة السريعة

### مشاكل شائعة:
- **"لا يوجد مستخدمين"**: أضف مستخدم جديد أولاً
- **"لا توجد بيانات"**: سجل بعض القياسات
- **خطأ في التاريخ**: استخدم التنسيق الصحيح YYYY-MM-DD

### للحصول على مساعدة مفصلة:
راجع ملف `دليل_المستخدم.md` للتعليمات الكاملة.

---
**نصيحة**: ابدأ بإضافة مستخدم جديد، ثم سجل بعض القياسات، وجرب الرسوم البيانية والتقارير!
