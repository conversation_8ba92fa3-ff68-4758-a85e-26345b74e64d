#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة تصدير التقارير
Reports Export Module

تحتوي على فئات وطرق تصدير التقارير إلى PDF وExcel
مع دعم اللغة العربية والتنسيق المناسب

المطور: مساعد الذكي الاصطناعي
التاريخ: 2025-06-21
"""

from reportlab.lib.pagesizes import A4, letter
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.platypus import PageBreak, Image
from reportlab.pdfgen import canvas
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont

import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.chart import LineChart, Reference
import xlsxwriter

import os
import logging
from datetime import datetime, date
from typing import List, Dict, Optional
import tempfile

logger = logging.getLogger(__name__)

class ReportExporter:
    """مصدر التقارير الرئيسي"""
    
    def __init__(self, db_manager):
        """
        تهيئة مصدر التقارير
        
        Args:
            db_manager: مدير قاعدة البيانات
        """
        self.db_manager = db_manager
        self.reports_dir = "reports"
        
        # إنشاء مجلد التقارير إذا لم يكن موجوداً
        os.makedirs(self.reports_dir, exist_ok=True)
        
        # إعداد الخطوط للـ PDF
        self.setup_fonts()
    
    def setup_fonts(self):
        """إعداد الخطوط للتقارير"""
        try:
            # محاولة تسجيل خط عربي
            # يمكن إضافة ملفات خطوط عربية في مجلد assets
            font_paths = [
                "assets/arial.ttf",
                "assets/tahoma.ttf",
                "C:/Windows/Fonts/arial.ttf",
                "C:/Windows/Fonts/tahoma.ttf"
            ]
            
            self.arabic_font_registered = False
            for font_path in font_paths:
                if os.path.exists(font_path):
                    try:
                        pdfmetrics.registerFont(TTFont('Arabic', font_path))
                        self.arabic_font_registered = True
                        break
                    except:
                        continue
            
            if not self.arabic_font_registered:
                logger.warning("لم يتم العثور على خط عربي، سيتم استخدام الخط الافتراضي")
                
        except Exception as e:
            logger.warning(f"تحذير في إعداد الخطوط: {e}")
            self.arabic_font_registered = False
    
    def export_measurements_to_excel(self, user_id: int, start_date: date = None, 
                                   end_date: date = None, filename: str = None) -> str:
        """
        تصدير القياسات إلى Excel
        
        Args:
            user_id: معرف المستخدم
            start_date: تاريخ البداية
            end_date: تاريخ النهاية
            filename: اسم الملف
            
        Returns:
            مسار الملف المُصدر
        """
        try:
            # إنشاء اسم الملف إذا لم يتم تحديده
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"measurements_user_{user_id}_{timestamp}.xlsx"
            
            filepath = os.path.join(self.reports_dir, filename)
            
            # الحصول على بيانات المستخدم والقياسات
            users = self.db_manager.get_users()
            user = next((u for u in users if u['id'] == user_id), None)
            
            if not user:
                raise ValueError(f"المستخدم غير موجود: {user_id}")
            
            measurements = self.db_manager.get_measurements(
                user_id=user_id,
                start_date=start_date,
                end_date=end_date
            )
            
            # إنشاء ملف Excel
            workbook = openpyxl.Workbook()
            
            # ورقة معلومات المستخدم
            user_sheet = workbook.active
            user_sheet.title = "معلومات المستخدم"
            
            # إضافة معلومات المستخدم
            user_data = [
                ["الاسم", user['name']],
                ["العمر", user['age'] or "غير محدد"],
                ["الجنس", user['gender'] or "غير محدد"],
                ["الحالات الطبية", user['medical_conditions'] or "لا توجد"],
                ["الأدوية", user['medications'] or "لا توجد"],
                ["تاريخ الإنشاء", user['created_at']],
                ["", ""],
                ["فترة التقرير", ""],
                ["من تاريخ", start_date.strftime("%Y-%m-%d") if start_date else "البداية"],
                ["إلى تاريخ", end_date.strftime("%Y-%m-%d") if end_date else "النهاية"],
                ["عدد القياسات", len(measurements)],
                ["تاريخ التصدير", datetime.now().strftime("%Y-%m-%d %H:%M:%S")]
            ]
            
            for row_idx, (key, value) in enumerate(user_data, 1):
                user_sheet.cell(row=row_idx, column=1, value=key)
                user_sheet.cell(row=row_idx, column=2, value=value)
            
            # تنسيق ورقة المستخدم
            self._format_excel_sheet(user_sheet, "معلومات المستخدم")
            
            # ورقة القياسات
            measurements_sheet = workbook.create_sheet("القياسات")
            
            # عناوين الأعمدة
            headers = [
                "التاريخ", "الوقت", "الضغط الانقباضي", "الضغط الانبساطي",
                "مستوى السكر", "نوع قياس السكر", "ملاحظات"
            ]
            
            for col_idx, header in enumerate(headers, 1):
                cell = measurements_sheet.cell(row=1, column=col_idx, value=header)
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                cell.font = Font(color="FFFFFF", bold=True)
            
            # إضافة بيانات القياسات
            for row_idx, measurement in enumerate(measurements, 2):
                measurements_sheet.cell(row=row_idx, column=1, value=measurement['measurement_date'])
                measurements_sheet.cell(row=row_idx, column=2, value=measurement['measurement_time'])
                measurements_sheet.cell(row=row_idx, column=3, value=measurement['systolic_pressure'])
                measurements_sheet.cell(row=row_idx, column=4, value=measurement['diastolic_pressure'])
                measurements_sheet.cell(row=row_idx, column=5, value=measurement['blood_sugar'])
                measurements_sheet.cell(row=row_idx, column=6, 
                                      value="صائم" if measurement['sugar_type'] == 'fasting' else "بعد الأكل")
                measurements_sheet.cell(row=row_idx, column=7, value=measurement['notes'] or "")
            
            # تنسيق ورقة القياسات
            self._format_excel_sheet(measurements_sheet, "القياسات")
            
            # إضافة رسم بياني إذا كانت هناك بيانات كافية
            if len(measurements) > 1:
                self._add_excel_chart(measurements_sheet, len(measurements))
            
            # حفظ الملف
            workbook.save(filepath)
            logger.info(f"تم تصدير القياسات إلى Excel: {filepath}")
            
            return filepath
            
        except Exception as e:
            logger.error(f"خطأ في تصدير القياسات إلى Excel: {e}")
            raise
    
    def _format_excel_sheet(self, sheet, title: str):
        """تنسيق ورقة Excel"""
        try:
            # تعديل عرض الأعمدة
            for column in sheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                
                adjusted_width = min(max_length + 2, 50)
                sheet.column_dimensions[column_letter].width = adjusted_width
            
            # إضافة حدود للخلايا
            thin_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )
            
            for row in sheet.iter_rows():
                for cell in row:
                    if cell.value:
                        cell.border = thin_border
                        cell.alignment = Alignment(horizontal='center', vertical='center')
            
        except Exception as e:
            logger.warning(f"تحذير في تنسيق ورقة Excel: {e}")
    
    def _add_excel_chart(self, sheet, data_rows: int):
        """إضافة رسم بياني إلى ورقة Excel"""
        try:
            # رسم بياني لضغط الدم
            chart = LineChart()
            chart.title = "تطور ضغط الدم"
            chart.style = 13
            chart.x_axis.title = "القياسات"
            chart.y_axis.title = "ضغط الدم (mmHg)"
            
            # بيانات الضغط الانقباضي
            systolic_data = Reference(sheet, min_col=3, min_row=1, max_row=data_rows + 1)
            chart.add_data(systolic_data, titles_from_data=True)
            
            # بيانات الضغط الانبساطي
            diastolic_data = Reference(sheet, min_col=4, min_row=1, max_row=data_rows + 1)
            chart.add_data(diastolic_data, titles_from_data=True)
            
            # إضافة الرسم إلى الورقة
            sheet.add_chart(chart, "I2")
            
        except Exception as e:
            logger.warning(f"تحذير في إضافة الرسم البياني: {e}")
    
    def export_measurements_to_pdf(self, user_id: int, start_date: date = None, 
                                 end_date: date = None, filename: str = None) -> str:
        """
        تصدير القياسات إلى PDF
        
        Args:
            user_id: معرف المستخدم
            start_date: تاريخ البداية
            end_date: تاريخ النهاية
            filename: اسم الملف
            
        Returns:
            مسار الملف المُصدر
        """
        try:
            # إنشاء اسم الملف إذا لم يتم تحديده
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"measurements_user_{user_id}_{timestamp}.pdf"
            
            filepath = os.path.join(self.reports_dir, filename)
            
            # الحصول على بيانات المستخدم والقياسات
            users = self.db_manager.get_users()
            user = next((u for u in users if u['id'] == user_id), None)
            
            if not user:
                raise ValueError(f"المستخدم غير موجود: {user_id}")
            
            measurements = self.db_manager.get_measurements(
                user_id=user_id,
                start_date=start_date,
                end_date=end_date
            )
            
            # إنشاء مستند PDF
            doc = SimpleDocTemplate(filepath, pagesize=A4)
            story = []
            
            # إعداد الأنماط
            styles = getSampleStyleSheet()
            
            # نمط العنوان الرئيسي
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=30,
                alignment=1,  # وسط
                fontName='Arabic' if self.arabic_font_registered else 'Helvetica-Bold'
            )
            
            # نمط العنوان الفرعي
            subtitle_style = ParagraphStyle(
                'CustomSubtitle',
                parent=styles['Heading2'],
                fontSize=14,
                spaceAfter=20,
                fontName='Arabic' if self.arabic_font_registered else 'Helvetica-Bold'
            )
            
            # نمط النص العادي
            normal_style = ParagraphStyle(
                'CustomNormal',
                parent=styles['Normal'],
                fontSize=12,
                spaceAfter=12,
                fontName='Arabic' if self.arabic_font_registered else 'Helvetica'
            )
            
            # العنوان الرئيسي
            story.append(Paragraph("تقرير قياسات ضغط الدم والسكر", title_style))
            story.append(Spacer(1, 20))
            
            # معلومات المستخدم
            story.append(Paragraph("معلومات المستخدم", subtitle_style))
            
            user_info = [
                f"الاسم: {user['name']}",
                f"العمر: {user['age'] or 'غير محدد'}",
                f"الجنس: {user['gender'] or 'غير محدد'}",
                f"الحالات الطبية: {user['medical_conditions'] or 'لا توجد'}",
                f"الأدوية: {user['medications'] or 'لا توجد'}"
            ]
            
            for info in user_info:
                story.append(Paragraph(info, normal_style))
            
            story.append(Spacer(1, 20))
            
            # معلومات التقرير
            story.append(Paragraph("معلومات التقرير", subtitle_style))
            
            report_info = [
                f"فترة التقرير: من {start_date.strftime('%Y-%m-%d') if start_date else 'البداية'} إلى {end_date.strftime('%Y-%m-%d') if end_date else 'النهاية'}",
                f"عدد القياسات: {len(measurements)}",
                f"تاريخ التصدير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            ]
            
            for info in report_info:
                story.append(Paragraph(info, normal_style))
            
            story.append(Spacer(1, 30))
            
            # جدول القياسات
            if measurements:
                story.append(Paragraph("سجل القياسات", subtitle_style))
                
                # إعداد بيانات الجدول
                table_data = [
                    ["التاريخ", "الوقت", "الضغط الانقباضي", "الضغط الانبساطي", "السكر", "النوع", "ملاحظات"]
                ]
                
                for measurement in measurements:
                    row = [
                        str(measurement['measurement_date']),
                        str(measurement['measurement_time']),
                        str(measurement['systolic_pressure'] or '--'),
                        str(measurement['diastolic_pressure'] or '--'),
                        str(measurement['blood_sugar'] or '--'),
                        "صائم" if measurement['sugar_type'] == 'fasting' else "بعد الأكل",
                        str(measurement['notes'] or '')[:20] + "..." if len(str(measurement['notes'] or '')) > 20 else str(measurement['notes'] or '')
                    ]
                    table_data.append(row)
                
                # إنشاء الجدول
                table = Table(table_data)
                table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 10),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('FONTSIZE', (0, 1), (-1, -1), 8),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                
                story.append(table)
            else:
                story.append(Paragraph("لا توجد قياسات في الفترة المحددة", normal_style))
            
            # بناء المستند
            doc.build(story)
            logger.info(f"تم تصدير القياسات إلى PDF: {filepath}")
            
            return filepath
            
        except Exception as e:
            logger.error(f"خطأ في تصدير القياسات إلى PDF: {e}")
            raise
    
    def export_health_report_to_pdf(self, user_id: int, health_report: Dict, 
                                  filename: str = None) -> str:
        """
        تصدير التقرير الصحي إلى PDF
        
        Args:
            user_id: معرف المستخدم
            health_report: التقرير الصحي
            filename: اسم الملف
            
        Returns:
            مسار الملف المُصدر
        """
        try:
            # إنشاء اسم الملف إذا لم يتم تحديده
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"health_report_user_{user_id}_{timestamp}.pdf"
            
            filepath = os.path.join(self.reports_dir, filename)
            
            # إنشاء مستند PDF
            doc = SimpleDocTemplate(filepath, pagesize=A4)
            story = []
            
            # إعداد الأنماط
            styles = getSampleStyleSheet()
            
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=30,
                alignment=1,
                fontName='Arabic' if self.arabic_font_registered else 'Helvetica-Bold'
            )
            
            subtitle_style = ParagraphStyle(
                'CustomSubtitle',
                parent=styles['Heading2'],
                fontSize=14,
                spaceAfter=20,
                fontName='Arabic' if self.arabic_font_registered else 'Helvetica-Bold'
            )
            
            normal_style = ParagraphStyle(
                'CustomNormal',
                parent=styles['Normal'],
                fontSize=12,
                spaceAfter=12,
                fontName='Arabic' if self.arabic_font_registered else 'Helvetica'
            )
            
            # العنوان الرئيسي
            story.append(Paragraph("التقرير الصحي الشامل", title_style))
            story.append(Spacer(1, 20))
            
            # معلومات التقرير
            story.append(Paragraph("معلومات التقرير", subtitle_style))
            story.append(Paragraph(f"فترة التحليل: {health_report.get('period', 'غير محدد')}", normal_style))
            story.append(Paragraph(f"تاريخ الإنشاء: {health_report.get('generated_at', 'غير محدد')}", normal_style))
            story.append(Spacer(1, 20))
            
            # الإحصائيات
            if 'statistics' in health_report:
                stats = health_report['statistics']
                
                story.append(Paragraph("الإحصائيات", subtitle_style))
                
                # إحصائيات ضغط الدم
                if 'blood_pressure' in stats:
                    bp_stats = stats['blood_pressure']
                    story.append(Paragraph("ضغط الدم:", normal_style))
                    story.append(Paragraph(f"• عدد القياسات: {bp_stats['count']}", normal_style))
                    story.append(Paragraph(f"• متوسط الضغط الانقباضي: {bp_stats['systolic']['mean']}", normal_style))
                    story.append(Paragraph(f"• متوسط الضغط الانبساطي: {bp_stats['diastolic']['mean']}", normal_style))
                    story.append(Spacer(1, 10))
                
                # إحصائيات السكر
                if 'blood_sugar' in stats:
                    sugar_stats = stats['blood_sugar']
                    story.append(Paragraph("مستوى السكر:", normal_style))
                    story.append(Paragraph(f"• عدد القياسات: {sugar_stats['count']}", normal_style))
                    story.append(Paragraph(f"• متوسط مستوى السكر: {sugar_stats['overall']['mean']}", normal_style))
                    story.append(Spacer(1, 10))
            
            # التنبيهات
            if health_report.get('alerts'):
                story.append(Paragraph("التنبيهات الصحية", subtitle_style))
                for alert in health_report['alerts']:
                    story.append(Paragraph(f"⚠️ {alert}", normal_style))
                story.append(Spacer(1, 20))
            
            # التوصيات
            if health_report.get('recommendations'):
                story.append(Paragraph("التوصيات الصحية", subtitle_style))
                for i, recommendation in enumerate(health_report['recommendations'], 1):
                    story.append(Paragraph(f"{i}. {recommendation}", normal_style))
            
            # بناء المستند
            doc.build(story)
            logger.info(f"تم تصدير التقرير الصحي إلى PDF: {filepath}")
            
            return filepath
            
        except Exception as e:
            logger.error(f"خطأ في تصدير التقرير الصحي إلى PDF: {e}")
            raise
    
    def get_available_reports(self) -> List[Dict]:
        """الحصول على قائمة التقارير المتاحة"""
        try:
            reports = []
            
            if os.path.exists(self.reports_dir):
                for filename in os.listdir(self.reports_dir):
                    if filename.endswith(('.pdf', '.xlsx')):
                        filepath = os.path.join(self.reports_dir, filename)
                        stat = os.stat(filepath)
                        
                        reports.append({
                            'filename': filename,
                            'filepath': filepath,
                            'size': stat.st_size,
                            'created_at': datetime.fromtimestamp(stat.st_ctime),
                            'modified_at': datetime.fromtimestamp(stat.st_mtime),
                            'type': 'PDF' if filename.endswith('.pdf') else 'Excel'
                        })
            
            # ترتيب حسب تاريخ التعديل (الأحدث أولاً)
            reports.sort(key=lambda x: x['modified_at'], reverse=True)
            
            return reports
            
        except Exception as e:
            logger.error(f"خطأ في جلب التقارير المتاحة: {e}")
            return []
