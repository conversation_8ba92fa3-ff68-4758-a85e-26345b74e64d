@echo off
chcp 65001 >nul
title برنامج ضغط وسكر - حفظ القياسات وتحليلها

echo.
echo ========================================
echo    برنامج ضغط وسكر - حفظ القياسات وتحليلها
echo    Blood Pressure ^& Sugar Tracker
echo ========================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام
    echo يرجى تثبيت Python 3.8 أو أحدث من: https://python.org
    pause
    exit /b 1
)

echo ✅ تم العثور على Python
python --version

REM التحقق من وجود المتطلبات
echo.
echo 🔍 فحص المتطلبات...

python -c "import pandas, matplotlib, reportlab" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  بعض المكتبات المطلوبة غير مثبتة
    echo.
    set /p install_deps="هل تريد تثبيت المتطلبات الآن؟ (y/n): "
    if /i "%install_deps%"=="y" (
        echo.
        echo 📦 جاري تثبيت المتطلبات...
        python install_requirements.py
        echo.
        pause
    ) else (
        echo يمكنك تثبيت المتطلبات لاحقاً بتشغيل: python install_requirements.py
        echo.
    )
)

echo.
echo 🚀 بدء تشغيل البرنامج...
echo.

REM تشغيل البرنامج
python main.py

REM في حالة إغلاق البرنامج
echo.
echo 👋 تم إغلاق البرنامج
pause
