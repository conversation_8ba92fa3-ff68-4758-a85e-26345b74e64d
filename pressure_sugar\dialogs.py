#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نوافذ الحوار والنماذج
Dialog Windows and Forms Module

تحتوي على نوافذ الحوار لإضافة المستخدمين والقياسات
والإعدادات وغيرها من النماذج التفاعلية

المطور: مساعد الذكي الاصطناعي
التاريخ: 2025-06-21
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, date, time
import logging
from typing import Optional, Dict, Tuple

logger = logging.getLogger(__name__)

class NewUserDialog:
    """نافذة إضافة مستخدم جديد"""
    
    def __init__(self, parent, db_manager):
        """
        إنشاء نافذة إضافة مستخدم جديد
        
        Args:
            parent: النافذة الأب
            db_manager: مدير قاعدة البيانات
        """
        self.parent = parent
        self.db_manager = db_manager
        self.result = None
        
        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("إضافة مستخدم جديد")
        self.dialog.geometry("400x500")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # وضع النافذة في المنتصف
        self.center_window()
        
        # متغيرات النموذج
        self.name_var = tk.StringVar()
        self.age_var = tk.StringVar()
        self.gender_var = tk.StringVar(value="ذكر")
        self.conditions_var = tk.StringVar()
        self.medications_var = tk.StringVar()
        
        self.create_widgets()
    
    def center_window(self):
        """وضع النافذة في منتصف الشاشة"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (400 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (500 // 2)
        self.dialog.geometry(f"400x500+{x}+{y}")
    
    def create_widgets(self):
        """إنشاء عناصر النافذة"""
        # إطار رئيسي
        main_frame = tk.Frame(self.dialog, padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # عنوان
        title_label = tk.Label(
            main_frame,
            text="إضافة مستخدم جديد",
            font=("Arial", 16, "bold"),
            fg="#2E86AB"
        )
        title_label.pack(pady=(0, 20))
        
        # حقل الاسم
        tk.Label(main_frame, text="الاسم *", font=("Arial", 12)).pack(anchor=tk.W)
        name_entry = tk.Entry(
            main_frame,
            textvariable=self.name_var,
            font=("Arial", 12),
            width=30
        )
        name_entry.pack(fill=tk.X, pady=(5, 15))
        name_entry.focus()
        
        # حقل العمر
        tk.Label(main_frame, text="العمر", font=("Arial", 12)).pack(anchor=tk.W)
        age_entry = tk.Entry(
            main_frame,
            textvariable=self.age_var,
            font=("Arial", 12),
            width=30
        )
        age_entry.pack(fill=tk.X, pady=(5, 15))
        
        # حقل الجنس
        tk.Label(main_frame, text="الجنس", font=("Arial", 12)).pack(anchor=tk.W)
        gender_frame = tk.Frame(main_frame)
        gender_frame.pack(fill=tk.X, pady=(5, 15))
        
        tk.Radiobutton(
            gender_frame,
            text="ذكر",
            variable=self.gender_var,
            value="ذكر",
            font=("Arial", 11)
        ).pack(side=tk.LEFT)
        
        tk.Radiobutton(
            gender_frame,
            text="أنثى",
            variable=self.gender_var,
            value="أنثى",
            font=("Arial", 11)
        ).pack(side=tk.LEFT, padx=(20, 0))
        
        # حقل الحالات الطبية
        tk.Label(main_frame, text="الحالات الطبية", font=("Arial", 12)).pack(anchor=tk.W)
        conditions_text = tk.Text(
            main_frame,
            height=4,
            font=("Arial", 11),
            wrap=tk.WORD
        )
        conditions_text.pack(fill=tk.X, pady=(5, 15))
        
        # ربط النص بالمتغير
        def update_conditions(*args):
            self.conditions_var.set(conditions_text.get("1.0", tk.END).strip())
        
        conditions_text.bind("<KeyRelease>", update_conditions)
        
        # حقل الأدوية
        tk.Label(main_frame, text="الأدوية الحالية", font=("Arial", 12)).pack(anchor=tk.W)
        medications_text = tk.Text(
            main_frame,
            height=4,
            font=("Arial", 11),
            wrap=tk.WORD
        )
        medications_text.pack(fill=tk.X, pady=(5, 20))
        
        # ربط النص بالمتغير
        def update_medications(*args):
            self.medications_var.set(medications_text.get("1.0", tk.END).strip())
        
        medications_text.bind("<KeyRelease>", update_medications)
        
        # أزرار
        buttons_frame = tk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X)
        
        cancel_button = tk.Button(
            buttons_frame,
            text="إلغاء",
            command=self.cancel_clicked,
            font=("Arial", 11),
            width=12,
            bg="#C73E1D",
            fg="white"
        )
        cancel_button.pack(side=tk.RIGHT, padx=(10, 0))
        
        save_button = tk.Button(
            buttons_frame,
            text="حفظ",
            command=self.save_clicked,
            font=("Arial", 11),
            width=12,
            bg="#2E86AB",
            fg="white"
        )
        save_button.pack(side=tk.RIGHT)
        
        # ربط مفتاح Enter
        self.dialog.bind('<Return>', lambda e: self.save_clicked())
        self.dialog.bind('<Escape>', lambda e: self.cancel_clicked())
    
    def save_clicked(self):
        """عند النقر على حفظ"""
        try:
            # التحقق من صحة البيانات
            if not self.name_var.get().strip():
                messagebox.showerror("خطأ", "يجب إدخال الاسم")
                return
            
            # التحقق من العمر
            age = None
            if self.age_var.get().strip():
                try:
                    age = int(self.age_var.get().strip())
                    if age < 0 or age > 150:
                        messagebox.showerror("خطأ", "العمر يجب أن يكون بين 0 و 150")
                        return
                except ValueError:
                    messagebox.showerror("خطأ", "العمر يجب أن يكون رقماً")
                    return
            
            # حفظ المستخدم
            user_id = self.db_manager.add_user(
                name=self.name_var.get().strip(),
                age=age,
                gender=self.gender_var.get(),
                medical_conditions=self.conditions_var.get().strip() or None,
                medications=self.medications_var.get().strip() or None
            )
            
            self.result = user_id
            messagebox.showinfo("نجح", f"تم إضافة المستخدم بنجاح")
            self.dialog.destroy()
            
        except Exception as e:
            logger.error(f"خطأ في حفظ المستخدم: {e}")
            messagebox.showerror("خطأ", f"فشل في حفظ المستخدم:\n{e}")
    
    def cancel_clicked(self):
        """عند النقر على إلغاء"""
        self.result = None
        self.dialog.destroy()
    
    def show(self):
        """عرض النافذة وانتظار النتيجة"""
        self.dialog.wait_window()
        return self.result

class NewMeasurementDialog:
    """نافذة إضافة قياس جديد"""
    
    def __init__(self, parent, db_manager, user_id=None):
        """
        إنشاء نافذة إضافة قياس جديد
        
        Args:
            parent: النافذة الأب
            db_manager: مدير قاعدة البيانات
            user_id: معرف المستخدم (اختياري)
        """
        self.parent = parent
        self.db_manager = db_manager
        self.user_id = user_id
        self.result = None
        
        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("إضافة قياس جديد")
        self.dialog.geometry("450x600")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # وضع النافذة في المنتصف
        self.center_window()
        
        # متغيرات النموذج
        self.selected_user_var = tk.StringVar()
        self.date_var = tk.StringVar(value=date.today().strftime("%Y-%m-%d"))
        self.time_var = tk.StringVar(value=datetime.now().strftime("%H:%M"))
        self.systolic_var = tk.StringVar()
        self.diastolic_var = tk.StringVar()
        self.sugar_var = tk.StringVar()
        self.sugar_type_var = tk.StringVar(value="fasting")
        self.notes_var = tk.StringVar()
        
        # تحميل المستخدمين
        self.users = self.db_manager.get_users()
        
        self.create_widgets()
    
    def center_window(self):
        """وضع النافذة في منتصف الشاشة"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (450 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (600 // 2)
        self.dialog.geometry(f"450x600+{x}+{y}")
    
    def create_widgets(self):
        """إنشاء عناصر النافذة"""
        # إطار رئيسي
        main_frame = tk.Frame(self.dialog, padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # عنوان
        title_label = tk.Label(
            main_frame,
            text="إضافة قياس جديد",
            font=("Arial", 16, "bold"),
            fg="#2E86AB"
        )
        title_label.pack(pady=(0, 20))
        
        # اختيار المستخدم
        if not self.user_id:
            tk.Label(main_frame, text="المستخدم *", font=("Arial", 12)).pack(anchor=tk.W)
            user_combo = ttk.Combobox(
                main_frame,
                textvariable=self.selected_user_var,
                font=("Arial", 11),
                state="readonly",
                width=35
            )
            
            user_names = [f"{user['name']} (ID: {user['id']})" for user in self.users]
            user_combo['values'] = user_names
            
            if self.users:
                user_combo.current(0)
            
            user_combo.pack(fill=tk.X, pady=(5, 15))
        
        # التاريخ والوقت
        datetime_frame = tk.Frame(main_frame)
        datetime_frame.pack(fill=tk.X, pady=(0, 15))
        
        # التاريخ
        date_frame = tk.Frame(datetime_frame)
        date_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        tk.Label(date_frame, text="التاريخ *", font=("Arial", 12)).pack(anchor=tk.W)
        date_entry = tk.Entry(
            date_frame,
            textvariable=self.date_var,
            font=("Arial", 11),
            width=15
        )
        date_entry.pack(fill=tk.X, pady=(5, 0))
        
        # الوقت
        time_frame = tk.Frame(datetime_frame)
        time_frame.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(10, 0))
        
        tk.Label(time_frame, text="الوقت *", font=("Arial", 12)).pack(anchor=tk.W)
        time_entry = tk.Entry(
            time_frame,
            textvariable=self.time_var,
            font=("Arial", 11),
            width=15
        )
        time_entry.pack(fill=tk.X, pady=(5, 0))
        
        # ضغط الدم
        bp_frame = tk.LabelFrame(main_frame, text="ضغط الدم", font=("Arial", 12, "bold"))
        bp_frame.pack(fill=tk.X, pady=(0, 15))
        
        bp_inner_frame = tk.Frame(bp_frame, padx=10, pady=10)
        bp_inner_frame.pack(fill=tk.X)
        
        # الضغط الانقباضي
        systolic_frame = tk.Frame(bp_inner_frame)
        systolic_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        tk.Label(systolic_frame, text="الانقباضي", font=("Arial", 11)).pack(anchor=tk.W)
        systolic_entry = tk.Entry(
            systolic_frame,
            textvariable=self.systolic_var,
            font=("Arial", 11),
            width=10
        )
        systolic_entry.pack(fill=tk.X, pady=(5, 0))
        
        # الضغط الانبساطي
        diastolic_frame = tk.Frame(bp_inner_frame)
        diastolic_frame.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(10, 0))
        
        tk.Label(diastolic_frame, text="الانبساطي", font=("Arial", 11)).pack(anchor=tk.W)
        diastolic_entry = tk.Entry(
            diastolic_frame,
            textvariable=self.diastolic_var,
            font=("Arial", 11),
            width=10
        )
        diastolic_entry.pack(fill=tk.X, pady=(5, 0))
        
        # السكر
        sugar_frame = tk.LabelFrame(main_frame, text="مستوى السكر", font=("Arial", 12, "bold"))
        sugar_frame.pack(fill=tk.X, pady=(0, 15))
        
        sugar_inner_frame = tk.Frame(sugar_frame, padx=10, pady=10)
        sugar_inner_frame.pack(fill=tk.X)
        
        # قيمة السكر
        sugar_value_frame = tk.Frame(sugar_inner_frame)
        sugar_value_frame.pack(fill=tk.X, pady=(0, 10))
        
        tk.Label(sugar_value_frame, text="القيمة (mg/dL)", font=("Arial", 11)).pack(anchor=tk.W)
        sugar_entry = tk.Entry(
            sugar_value_frame,
            textvariable=self.sugar_var,
            font=("Arial", 11),
            width=15
        )
        sugar_entry.pack(fill=tk.X, pady=(5, 0))
        
        # نوع القياس
        sugar_type_frame = tk.Frame(sugar_inner_frame)
        sugar_type_frame.pack(fill=tk.X)
        
        tk.Label(sugar_type_frame, text="نوع القياس", font=("Arial", 11)).pack(anchor=tk.W)
        
        type_buttons_frame = tk.Frame(sugar_type_frame)
        type_buttons_frame.pack(fill=tk.X, pady=(5, 0))
        
        tk.Radiobutton(
            type_buttons_frame,
            text="صائم",
            variable=self.sugar_type_var,
            value="fasting",
            font=("Arial", 10)
        ).pack(side=tk.LEFT)
        
        tk.Radiobutton(
            type_buttons_frame,
            text="بعد الأكل",
            variable=self.sugar_type_var,
            value="after_meal",
            font=("Arial", 10)
        ).pack(side=tk.LEFT, padx=(20, 0))
        
        # الملاحظات
        tk.Label(main_frame, text="ملاحظات", font=("Arial", 12)).pack(anchor=tk.W)
        notes_text = tk.Text(
            main_frame,
            height=4,
            font=("Arial", 11),
            wrap=tk.WORD
        )
        notes_text.pack(fill=tk.X, pady=(5, 20))
        
        # ربط النص بالمتغير
        def update_notes(*args):
            self.notes_var.set(notes_text.get("1.0", tk.END).strip())
        
        notes_text.bind("<KeyRelease>", update_notes)
        
        # أزرار
        buttons_frame = tk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X)
        
        cancel_button = tk.Button(
            buttons_frame,
            text="إلغاء",
            command=self.cancel_clicked,
            font=("Arial", 11),
            width=12,
            bg="#C73E1D",
            fg="white"
        )
        cancel_button.pack(side=tk.RIGHT, padx=(10, 0))
        
        save_button = tk.Button(
            buttons_frame,
            text="حفظ",
            command=self.save_clicked,
            font=("Arial", 11),
            width=12,
            bg="#2E86AB",
            fg="white"
        )
        save_button.pack(side=tk.RIGHT)
        
        # ربط مفاتيح الاختصار
        self.dialog.bind('<Return>', lambda e: self.save_clicked())
        self.dialog.bind('<Escape>', lambda e: self.cancel_clicked())
    
    def save_clicked(self):
        """عند النقر على حفظ"""
        try:
            # تحديد المستخدم
            if self.user_id:
                selected_user_id = self.user_id
            else:
                if not self.selected_user_var.get():
                    messagebox.showerror("خطأ", "يجب اختيار المستخدم")
                    return
                
                # استخراج معرف المستخدم من النص المختار
                selected_text = self.selected_user_var.get()
                try:
                    selected_user_id = int(selected_text.split("ID: ")[1].split(")")[0])
                except:
                    messagebox.showerror("خطأ", "خطأ في اختيار المستخدم")
                    return
            
            # التحقق من التاريخ والوقت
            try:
                measurement_date = datetime.strptime(self.date_var.get(), "%Y-%m-%d").date()
            except ValueError:
                messagebox.showerror("خطأ", "تنسيق التاريخ غير صحيح (YYYY-MM-DD)")
                return
            
            try:
                measurement_time = datetime.strptime(self.time_var.get(), "%H:%M").time()
            except ValueError:
                messagebox.showerror("خطأ", "تنسيق الوقت غير صحيح (HH:MM)")
                return
            
            # التحقق من القياسات
            systolic = None
            diastolic = None
            blood_sugar = None
            
            if self.systolic_var.get().strip():
                try:
                    systolic = int(self.systolic_var.get().strip())
                    if systolic < 50 or systolic > 300:
                        messagebox.showerror("خطأ", "الضغط الانقباضي يجب أن يكون بين 50 و 300")
                        return
                except ValueError:
                    messagebox.showerror("خطأ", "الضغط الانقباضي يجب أن يكون رقماً")
                    return
            
            if self.diastolic_var.get().strip():
                try:
                    diastolic = int(self.diastolic_var.get().strip())
                    if diastolic < 30 or diastolic > 200:
                        messagebox.showerror("خطأ", "الضغط الانبساطي يجب أن يكون بين 30 و 200")
                        return
                except ValueError:
                    messagebox.showerror("خطأ", "الضغط الانبساطي يجب أن يكون رقماً")
                    return
            
            if self.sugar_var.get().strip():
                try:
                    blood_sugar = float(self.sugar_var.get().strip())
                    if blood_sugar < 20 or blood_sugar > 600:
                        messagebox.showerror("خطأ", "مستوى السكر يجب أن يكون بين 20 و 600")
                        return
                except ValueError:
                    messagebox.showerror("خطأ", "مستوى السكر يجب أن يكون رقماً")
                    return
            
            # التحقق من وجود قياس واحد على الأقل
            if not systolic and not diastolic and not blood_sugar:
                messagebox.showerror("خطأ", "يجب إدخال قياس واحد على الأقل")
                return
            
            # التحقق من اكتمال قياس ضغط الدم
            if (systolic and not diastolic) or (diastolic and not systolic):
                messagebox.showerror("خطأ", "يجب إدخال كلا من الضغط الانقباضي والانبساطي")
                return
            
            # حفظ القياس
            measurement_id = self.db_manager.add_measurement(
                user_id=selected_user_id,
                measurement_date=measurement_date,
                measurement_time=self.time_var.get(),
                systolic_pressure=systolic,
                diastolic_pressure=diastolic,
                blood_sugar=blood_sugar,
                sugar_type=self.sugar_type_var.get(),
                notes=self.notes_var.get().strip() or None
            )
            
            self.result = measurement_id
            messagebox.showinfo("نجح", "تم حفظ القياس بنجاح")
            self.dialog.destroy()
            
        except Exception as e:
            logger.error(f"خطأ في حفظ القياس: {e}")
            messagebox.showerror("خطأ", f"فشل في حفظ القياس:\n{e}")
    
    def cancel_clicked(self):
        """عند النقر على إلغاء"""
        self.result = None
        self.dialog.destroy()
    
    def show(self):
        """عرض النافذة وانتظار النتيجة"""
        self.dialog.wait_window()
        return self.result
