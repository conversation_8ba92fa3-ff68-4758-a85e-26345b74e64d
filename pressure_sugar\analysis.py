#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة تحليل البيانات والرسوم البيانية
Data Analysis and Charts Module

تحتوي على فئات وطرق تحليل قياسات ضغط الدم والسكر
وإنشاء الرسوم البيانية باستخدام matplotlib

المطور: مساعد الذكي الاصطناعي
التاريخ: 2025-06-21
"""

import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
from typing import List, Dict, Optional, Tuple
import logging

# إعداد matplotlib للغة العربية
plt.rcParams['font.family'] = ['Arial Unicode MS', 'Tahoma', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

logger = logging.getLogger(__name__)

class DataAnalyzer:
    """محلل البيانات الرئيسي"""
    
    def __init__(self, db_manager):
        """
        تهيئة محلل البيانات
        
        Args:
            db_manager: مدير قاعدة البيانات
        """
        self.db_manager = db_manager
        
        # القيم المرجعية الطبيعية
        self.normal_ranges = {
            'systolic_normal': 120,
            'diastolic_normal': 80,
            'systolic_high': 140,
            'diastolic_high': 90,
            'sugar_fasting_normal': 100,
            'sugar_after_meal_normal': 140,
            'sugar_high': 180,
            'sugar_low': 70
        }
    
    def get_measurements_dataframe(self, user_id: int, start_date: date = None, 
                                 end_date: date = None) -> pd.DataFrame:
        """
        الحصول على القياسات كـ DataFrame
        
        Args:
            user_id: معرف المستخدم
            start_date: تاريخ البداية
            end_date: تاريخ النهاية
            
        Returns:
            DataFrame يحتوي على القياسات
        """
        try:
            measurements = self.db_manager.get_measurements(
                user_id=user_id,
                start_date=start_date,
                end_date=end_date
            )
            
            if not measurements:
                return pd.DataFrame()
            
            # تحويل إلى DataFrame
            df = pd.DataFrame(measurements)
            
            # تحويل التواريخ
            df['measurement_date'] = pd.to_datetime(df['measurement_date'])
            df['datetime'] = pd.to_datetime(
                df['measurement_date'].astype(str) + ' ' + df['measurement_time'].astype(str)
            )
            
            # ترتيب حسب التاريخ والوقت
            df = df.sort_values('datetime')
            
            return df
            
        except Exception as e:
            logger.error(f"خطأ في تحويل القياسات إلى DataFrame: {e}")
            return pd.DataFrame()
    
    def calculate_statistics(self, user_id: int, days: int = 30) -> Dict:
        """
        حساب الإحصائيات للمستخدم
        
        Args:
            user_id: معرف المستخدم
            days: عدد الأيام للتحليل
            
        Returns:
            قاموس يحتوي على الإحصائيات
        """
        try:
            end_date = date.today()
            start_date = end_date - timedelta(days=days)
            
            df = self.get_measurements_dataframe(user_id, start_date, end_date)
            
            if df.empty:
                return {}
            
            stats = {
                'period_days': days,
                'total_measurements': len(df),
                'date_range': {
                    'start': start_date.strftime('%Y-%m-%d'),
                    'end': end_date.strftime('%Y-%m-%d')
                }
            }
            
            # إحصائيات ضغط الدم
            bp_data = df.dropna(subset=['systolic_pressure', 'diastolic_pressure'])
            if not bp_data.empty:
                stats['blood_pressure'] = {
                    'count': len(bp_data),
                    'systolic': {
                        'mean': round(bp_data['systolic_pressure'].mean(), 1),
                        'min': int(bp_data['systolic_pressure'].min()),
                        'max': int(bp_data['systolic_pressure'].max()),
                        'std': round(bp_data['systolic_pressure'].std(), 1)
                    },
                    'diastolic': {
                        'mean': round(bp_data['diastolic_pressure'].mean(), 1),
                        'min': int(bp_data['diastolic_pressure'].min()),
                        'max': int(bp_data['diastolic_pressure'].max()),
                        'std': round(bp_data['diastolic_pressure'].std(), 1)
                    }
                }
                
                # تصنيف قراءات ضغط الدم
                stats['blood_pressure']['categories'] = self._categorize_blood_pressure(bp_data)
            
            # إحصائيات السكر
            sugar_data = df.dropna(subset=['blood_sugar'])
            if not sugar_data.empty:
                stats['blood_sugar'] = {
                    'count': len(sugar_data),
                    'overall': {
                        'mean': round(sugar_data['blood_sugar'].mean(), 1),
                        'min': round(sugar_data['blood_sugar'].min(), 1),
                        'max': round(sugar_data['blood_sugar'].max(), 1),
                        'std': round(sugar_data['blood_sugar'].std(), 1)
                    }
                }
                
                # إحصائيات حسب نوع القياس
                fasting = sugar_data[sugar_data['sugar_type'] == 'fasting']
                after_meal = sugar_data[sugar_data['sugar_type'] == 'after_meal']
                
                if not fasting.empty:
                    stats['blood_sugar']['fasting'] = {
                        'count': len(fasting),
                        'mean': round(fasting['blood_sugar'].mean(), 1),
                        'min': round(fasting['blood_sugar'].min(), 1),
                        'max': round(fasting['blood_sugar'].max(), 1)
                    }
                
                if not after_meal.empty:
                    stats['blood_sugar']['after_meal'] = {
                        'count': len(after_meal),
                        'mean': round(after_meal['blood_sugar'].mean(), 1),
                        'min': round(after_meal['blood_sugar'].min(), 1),
                        'max': round(after_meal['blood_sugar'].max(), 1)
                    }
                
                # تصنيف قراءات السكر
                stats['blood_sugar']['categories'] = self._categorize_blood_sugar(sugar_data)
            
            return stats
            
        except Exception as e:
            logger.error(f"خطأ في حساب الإحصائيات: {e}")
            return {}
    
    def _categorize_blood_pressure(self, df: pd.DataFrame) -> Dict:
        """تصنيف قراءات ضغط الدم"""
        categories = {
            'normal': 0,      # طبيعي
            'elevated': 0,    # مرتفع قليلاً
            'stage1': 0,      # مرتفع المرحلة 1
            'stage2': 0,      # مرتفع المرحلة 2
            'crisis': 0       # أزمة ارتفاع ضغط
        }
        
        for _, row in df.iterrows():
            systolic = row['systolic_pressure']
            diastolic = row['diastolic_pressure']
            
            if systolic >= 180 or diastolic >= 120:
                categories['crisis'] += 1
            elif systolic >= 140 or diastolic >= 90:
                categories['stage2'] += 1
            elif systolic >= 130 or diastolic >= 80:
                categories['stage1'] += 1
            elif systolic >= 120:
                categories['elevated'] += 1
            else:
                categories['normal'] += 1
        
        return categories
    
    def _categorize_blood_sugar(self, df: pd.DataFrame) -> Dict:
        """تصنيف قراءات السكر"""
        categories = {
            'low': 0,      # منخفض
            'normal': 0,   # طبيعي
            'high': 0,     # مرتفع
            'very_high': 0 # مرتفع جداً
        }
        
        for _, row in df.iterrows():
            sugar = row['blood_sugar']
            sugar_type = row['sugar_type']
            
            if sugar < 70:
                categories['low'] += 1
            elif sugar_type == 'fasting':
                if sugar <= 100:
                    categories['normal'] += 1
                elif sugar <= 125:
                    categories['high'] += 1
                else:
                    categories['very_high'] += 1
            else:  # after_meal
                if sugar <= 140:
                    categories['normal'] += 1
                elif sugar <= 199:
                    categories['high'] += 1
                else:
                    categories['very_high'] += 1
        
        return categories
    
    def create_blood_pressure_chart(self, user_id: int, days: int = 30) -> plt.Figure:
        """
        إنشاء رسم بياني لضغط الدم
        
        Args:
            user_id: معرف المستخدم
            days: عدد الأيام
            
        Returns:
            Figure object
        """
        try:
            end_date = date.today()
            start_date = end_date - timedelta(days=days)
            
            df = self.get_measurements_dataframe(user_id, start_date, end_date)
            bp_data = df.dropna(subset=['systolic_pressure', 'diastolic_pressure'])
            
            if bp_data.empty:
                # إنشاء رسم فارغ
                fig, ax = plt.subplots(figsize=(10, 6))
                ax.text(0.5, 0.5, 'لا توجد بيانات ضغط الدم', 
                       ha='center', va='center', transform=ax.transAxes, fontsize=14)
                ax.set_title('رسم بياني لضغط الدم')
                return fig
            
            # إنشاء الرسم البياني
            fig, ax = plt.subplots(figsize=(12, 8))
            
            # رسم الضغط الانقباضي والانبساطي
            ax.plot(bp_data['datetime'], bp_data['systolic_pressure'], 
                   'o-', label='الضغط الانقباضي', color='red', linewidth=2, markersize=6)
            ax.plot(bp_data['datetime'], bp_data['diastolic_pressure'], 
                   'o-', label='الضغط الانبساطي', color='blue', linewidth=2, markersize=6)
            
            # خطوط المرجع
            ax.axhline(y=120, color='green', linestyle='--', alpha=0.7, label='الحد الطبيعي للانقباضي')
            ax.axhline(y=80, color='orange', linestyle='--', alpha=0.7, label='الحد الطبيعي للانبساطي')
            ax.axhline(y=140, color='red', linestyle='--', alpha=0.7, label='حد الارتفاع للانقباضي')
            ax.axhline(y=90, color='purple', linestyle='--', alpha=0.7, label='حد الارتفاع للانبساطي')
            
            # تنسيق الرسم
            ax.set_xlabel('التاريخ')
            ax.set_ylabel('ضغط الدم (mmHg)')
            ax.set_title(f'تطور ضغط الدم خلال {days} يوم الماضية')
            ax.legend()
            ax.grid(True, alpha=0.3)
            
            # تنسيق محور التاريخ
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
            ax.xaxis.set_major_locator(mdates.DayLocator(interval=max(1, days//10)))
            plt.xticks(rotation=45)
            
            plt.tight_layout()
            return fig
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء رسم ضغط الدم: {e}")
            # إرجاع رسم فارغ في حالة الخطأ
            fig, ax = plt.subplots(figsize=(10, 6))
            ax.text(0.5, 0.5, f'خطأ في إنشاء الرسم: {e}', 
                   ha='center', va='center', transform=ax.transAxes, fontsize=12)
            return fig
    
    def create_blood_sugar_chart(self, user_id: int, days: int = 30) -> plt.Figure:
        """
        إنشاء رسم بياني للسكر
        
        Args:
            user_id: معرف المستخدم
            days: عدد الأيام
            
        Returns:
            Figure object
        """
        try:
            end_date = date.today()
            start_date = end_date - timedelta(days=days)
            
            df = self.get_measurements_dataframe(user_id, start_date, end_date)
            sugar_data = df.dropna(subset=['blood_sugar'])
            
            if sugar_data.empty:
                # إنشاء رسم فارغ
                fig, ax = plt.subplots(figsize=(10, 6))
                ax.text(0.5, 0.5, 'لا توجد بيانات السكر', 
                       ha='center', va='center', transform=ax.transAxes, fontsize=14)
                ax.set_title('رسم بياني لمستوى السكر')
                return fig
            
            # إنشاء الرسم البياني
            fig, ax = plt.subplots(figsize=(12, 8))
            
            # فصل البيانات حسب نوع القياس
            fasting = sugar_data[sugar_data['sugar_type'] == 'fasting']
            after_meal = sugar_data[sugar_data['sugar_type'] == 'after_meal']
            
            # رسم قياسات السكر الصائم
            if not fasting.empty:
                ax.plot(fasting['datetime'], fasting['blood_sugar'], 
                       'o-', label='السكر الصائم', color='blue', linewidth=2, markersize=6)
            
            # رسم قياسات السكر بعد الأكل
            if not after_meal.empty:
                ax.plot(after_meal['datetime'], after_meal['blood_sugar'], 
                       's-', label='السكر بعد الأكل', color='red', linewidth=2, markersize=6)
            
            # خطوط المرجع
            ax.axhline(y=100, color='green', linestyle='--', alpha=0.7, label='الحد الطبيعي للصائم')
            ax.axhline(y=140, color='orange', linestyle='--', alpha=0.7, label='الحد الطبيعي بعد الأكل')
            ax.axhline(y=180, color='red', linestyle='--', alpha=0.7, label='حد الارتفاع')
            ax.axhline(y=70, color='purple', linestyle='--', alpha=0.7, label='حد الانخفاض')
            
            # تنسيق الرسم
            ax.set_xlabel('التاريخ')
            ax.set_ylabel('مستوى السكر (mg/dL)')
            ax.set_title(f'تطور مستوى السكر خلال {days} يوم الماضية')
            ax.legend()
            ax.grid(True, alpha=0.3)
            
            # تنسيق محور التاريخ
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
            ax.xaxis.set_major_locator(mdates.DayLocator(interval=max(1, days//10)))
            plt.xticks(rotation=45)
            
            plt.tight_layout()
            return fig
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء رسم السكر: {e}")
            # إرجاع رسم فارغ في حالة الخطأ
            fig, ax = plt.subplots(figsize=(10, 6))
            ax.text(0.5, 0.5, f'خطأ في إنشاء الرسم: {e}', 
                   ha='center', va='center', transform=ax.transAxes, fontsize=12)
            return fig
    
    def create_combined_chart(self, user_id: int, days: int = 30) -> plt.Figure:
        """
        إنشاء رسم بياني مدمج لضغط الدم والسكر
        
        Args:
            user_id: معرف المستخدم
            days: عدد الأيام
            
        Returns:
            Figure object
        """
        try:
            end_date = date.today()
            start_date = end_date - timedelta(days=days)
            
            df = self.get_measurements_dataframe(user_id, start_date, end_date)
            
            if df.empty:
                fig, ax = plt.subplots(figsize=(10, 6))
                ax.text(0.5, 0.5, 'لا توجد بيانات', 
                       ha='center', va='center', transform=ax.transAxes, fontsize=14)
                ax.set_title('الرسم البياني المدمج')
                return fig
            
            # إنشاء رسم بمحورين
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10), sharex=True)
            
            # الرسم الأول: ضغط الدم
            bp_data = df.dropna(subset=['systolic_pressure', 'diastolic_pressure'])
            if not bp_data.empty:
                ax1.plot(bp_data['datetime'], bp_data['systolic_pressure'], 
                        'o-', label='الضغط الانقباضي', color='red', linewidth=2)
                ax1.plot(bp_data['datetime'], bp_data['diastolic_pressure'], 
                        'o-', label='الضغط الانبساطي', color='blue', linewidth=2)
                ax1.axhline(y=120, color='green', linestyle='--', alpha=0.5)
                ax1.axhline(y=80, color='orange', linestyle='--', alpha=0.5)
                ax1.set_ylabel('ضغط الدم (mmHg)')
                ax1.set_title('ضغط الدم')
                ax1.legend()
                ax1.grid(True, alpha=0.3)
            
            # الرسم الثاني: السكر
            sugar_data = df.dropna(subset=['blood_sugar'])
            if not sugar_data.empty:
                fasting = sugar_data[sugar_data['sugar_type'] == 'fasting']
                after_meal = sugar_data[sugar_data['sugar_type'] == 'after_meal']
                
                if not fasting.empty:
                    ax2.plot(fasting['datetime'], fasting['blood_sugar'], 
                            'o-', label='السكر الصائم', color='blue', linewidth=2)
                
                if not after_meal.empty:
                    ax2.plot(after_meal['datetime'], after_meal['blood_sugar'], 
                            's-', label='السكر بعد الأكل', color='red', linewidth=2)
                
                ax2.axhline(y=100, color='green', linestyle='--', alpha=0.5)
                ax2.axhline(y=140, color='orange', linestyle='--', alpha=0.5)
                ax2.set_ylabel('مستوى السكر (mg/dL)')
                ax2.set_title('مستوى السكر')
                ax2.legend()
                ax2.grid(True, alpha=0.3)
            
            # تنسيق محور التاريخ
            ax2.set_xlabel('التاريخ')
            ax2.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
            ax2.xaxis.set_major_locator(mdates.DayLocator(interval=max(1, days//10)))
            plt.xticks(rotation=45)
            
            plt.suptitle(f'تطور القياسات خلال {days} يوم الماضية', fontsize=16)
            plt.tight_layout()
            return fig
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء الرسم المدمج: {e}")
            fig, ax = plt.subplots(figsize=(10, 6))
            ax.text(0.5, 0.5, f'خطأ في إنشاء الرسم: {e}', 
                   ha='center', va='center', transform=ax.transAxes, fontsize=12)
            return fig
    
    def generate_health_report(self, user_id: int, days: int = 30) -> Dict:
        """
        إنشاء تقرير صحي شامل
        
        Args:
            user_id: معرف المستخدم
            days: عدد الأيام للتحليل
            
        Returns:
            قاموس يحتوي على التقرير الصحي
        """
        try:
            stats = self.calculate_statistics(user_id, days)
            
            if not stats:
                return {'error': 'لا توجد بيانات كافية لإنشاء التقرير'}
            
            report = {
                'user_id': user_id,
                'period': f'{days} يوم',
                'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'statistics': stats,
                'recommendations': [],
                'alerts': []
            }
            
            # تحليل ضغط الدم وإضافة التوصيات
            if 'blood_pressure' in stats:
                bp_stats = stats['blood_pressure']
                avg_systolic = bp_stats['systolic']['mean']
                avg_diastolic = bp_stats['diastolic']['mean']
                
                if avg_systolic >= 140 or avg_diastolic >= 90:
                    report['alerts'].append('متوسط ضغط الدم مرتفع')
                    report['recommendations'].append('استشر طبيبك حول ضغط الدم المرتفع')
                    report['recommendations'].append('قلل من تناول الملح والدهون')
                    report['recommendations'].append('مارس الرياضة بانتظام')
                
                elif avg_systolic >= 120 or avg_diastolic >= 80:
                    report['recommendations'].append('راقب ضغط الدم بانتظام')
                    report['recommendations'].append('حافظ على نمط حياة صحي')
            
            # تحليل السكر وإضافة التوصيات
            if 'blood_sugar' in stats:
                sugar_stats = stats['blood_sugar']
                
                if 'fasting' in sugar_stats:
                    avg_fasting = sugar_stats['fasting']['mean']
                    if avg_fasting > 126:
                        report['alerts'].append('متوسط السكر الصائم مرتفع')
                        report['recommendations'].append('استشر طبيبك حول مستوى السكر')
                        report['recommendations'].append('اتبع نظام غذائي صحي')
                
                if 'after_meal' in sugar_stats:
                    avg_after_meal = sugar_stats['after_meal']['mean']
                    if avg_after_meal > 200:
                        report['alerts'].append('متوسط السكر بعد الأكل مرتفع')
                        report['recommendations'].append('راقب كمية الكربوهيدرات في الوجبات')
            
            # توصيات عامة
            if not report['recommendations']:
                report['recommendations'].append('استمر في المتابعة المنتظمة للقياسات')
                report['recommendations'].append('حافظ على نمط حياة صحي')
            
            return report
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء التقرير الصحي: {e}")
            return {'error': f'خطأ في إنشاء التقرير: {e}'}
