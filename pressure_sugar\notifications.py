#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة نظام التنبيهات والإشعارات
Notifications and Alerts System Module

تحتوي على فئات وطرق إدارة التنبيهات والإشعارات
للقراءات الخطيرة ومواعيد الأدوية

المطور: مساعد الذكي الاصطناعي
التاريخ: 2025-06-21
"""

import tkinter as tk
from tkinter import messagebox
import logging
from datetime import datetime, date, time, timedelta
from typing import List, Dict, Optional, Callable
import threading
import time as time_module
import json
import os

# محاولة استيراد مكتبة الإشعارات
try:
    from plyer import notification
    PLYER_AVAILABLE = True
except ImportError:
    PLYER_AVAILABLE = False
    logging.warning("مكتبة plyer غير متوفرة، سيتم استخدام إشعارات tkinter فقط")

logger = logging.getLogger(__name__)

class NotificationManager:
    """مدير النظام الرئيسي للتنبيهات والإشعارات"""
    
    def __init__(self, db_manager, root_window=None):
        """
        تهيئة مدير التنبيهات
        
        Args:
            db_manager: مدير قاعدة البيانات
            root_window: النافذة الرئيسية للتطبيق
        """
        self.db_manager = db_manager
        self.root_window = root_window
        self.is_running = False
        self.check_thread = None
        self.notification_callbacks = []
        
        # إعدادات التنبيهات
        self.settings = {
            'enabled': True,
            'check_interval': 300,  # 5 دقائق
            'show_desktop_notifications': PLYER_AVAILABLE,
            'show_popup_alerts': True,
            'sound_enabled': True,
            'medication_reminders': True,
            'measurement_reminders': True
        }
        
        # تحميل الإعدادات المحفوظة
        self.load_settings()
        
        # قائمة التنبيهات النشطة
        self.active_alerts = []
        
        # مؤقتات التذكير
        self.reminder_timers = {}
    
    def load_settings(self):
        """تحميل إعدادات التنبيهات من قاعدة البيانات"""
        try:
            # تحميل الإعدادات من قاعدة البيانات
            notifications_enabled = self.db_manager.get_setting('notifications_enabled')
            if notifications_enabled:
                self.settings['enabled'] = notifications_enabled == '1'
            
            # يمكن إضافة المزيد من الإعدادات هنا
            
        except Exception as e:
            logger.warning(f"تحذير في تحميل إعدادات التنبيهات: {e}")
    
    def save_settings(self):
        """حفظ إعدادات التنبيهات في قاعدة البيانات"""
        try:
            self.db_manager.set_setting('notifications_enabled', 
                                      '1' if self.settings['enabled'] else '0')
            
        except Exception as e:
            logger.error(f"خطأ في حفظ إعدادات التنبيهات: {e}")
    
    def start_monitoring(self):
        """بدء مراقبة التنبيهات"""
        if not self.settings['enabled'] or self.is_running:
            return
        
        self.is_running = True
        self.check_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.check_thread.start()
        logger.info("تم بدء مراقبة التنبيهات")
    
    def stop_monitoring(self):
        """إيقاف مراقبة التنبيهات"""
        self.is_running = False
        if self.check_thread:
            self.check_thread.join(timeout=1)
        logger.info("تم إيقاف مراقبة التنبيهات")
    
    def _monitoring_loop(self):
        """حلقة مراقبة التنبيهات الرئيسية"""
        while self.is_running:
            try:
                # فحص التنبيهات الجديدة
                self.check_for_alerts()
                
                # فحص تذكيرات الأدوية
                if self.settings['medication_reminders']:
                    self.check_medication_reminders()
                
                # فحص تذكيرات القياسات
                if self.settings['measurement_reminders']:
                    self.check_measurement_reminders()
                
                # انتظار قبل الفحص التالي
                time_module.sleep(self.settings['check_interval'])
                
            except Exception as e:
                logger.error(f"خطأ في حلقة مراقبة التنبيهات: {e}")
                time_module.sleep(60)  # انتظار دقيقة في حالة الخطأ
    
    def check_for_alerts(self):
        """فحص التنبيهات الجديدة من قاعدة البيانات"""
        try:
            # الحصول على التنبيهات غير المقروءة
            unread_alerts = self.db_manager.get_alerts(unread_only=True)
            
            for alert in unread_alerts:
                if alert['id'] not in [a['id'] for a in self.active_alerts]:
                    self.show_alert(alert)
                    self.active_alerts.append(alert)
            
        except Exception as e:
            logger.error(f"خطأ في فحص التنبيهات: {e}")
    
    def check_medication_reminders(self):
        """فحص تذكيرات الأدوية"""
        try:
            # هذه الوظيفة ستحتاج إلى تطوير إضافي
            # لإدارة مواعيد الأدوية والتذكير بها
            pass
            
        except Exception as e:
            logger.error(f"خطأ في فحص تذكيرات الأدوية: {e}")
    
    def check_measurement_reminders(self):
        """فحص تذكيرات القياسات"""
        try:
            # فحص إذا كان المستخدم لم يسجل قياسات اليوم
            users = self.db_manager.get_users()
            today = date.today()
            
            for user in users:
                today_measurements = self.db_manager.get_measurements(
                    user_id=user['id'],
                    start_date=today,
                    end_date=today
                )
                
                # إذا لم يكن هناك قياسات اليوم وقد مر وقت كافٍ
                current_time = datetime.now().time()
                reminder_time = time(9, 0)  # 9 صباحاً
                
                if (not today_measurements and 
                    current_time > reminder_time and 
                    f"measurement_reminder_{user['id']}_{today}" not in self.reminder_timers):
                    
                    self.show_measurement_reminder(user)
                    self.reminder_timers[f"measurement_reminder_{user['id']}_{today}"] = True
            
        except Exception as e:
            logger.error(f"خطأ في فحص تذكيرات القياسات: {e}")
    
    def show_alert(self, alert: Dict):
        """عرض تنبيه للمستخدم"""
        try:
            alert_message = alert['message']
            alert_type = alert.get('alert_type', 'info')
            
            # تحديد نوع التنبيه ولونه
            if alert_type in ['high_blood_pressure', 'high_blood_sugar']:
                icon_type = 'warning'
                title = "تحذير صحي"
            elif alert_type in ['low_blood_pressure', 'low_blood_sugar']:
                icon_type = 'warning'
                title = "تنبيه صحي"
            else:
                icon_type = 'info'
                title = "إشعار"
            
            # عرض إشعار سطح المكتب
            if self.settings['show_desktop_notifications'] and PLYER_AVAILABLE:
                self.show_desktop_notification(title, alert_message, icon_type)
            
            # عرض نافذة منبثقة
            if self.settings['show_popup_alerts'] and self.root_window:
                self.show_popup_alert(title, alert_message, icon_type)
            
            # تشغيل صوت التنبيه
            if self.settings['sound_enabled']:
                self.play_alert_sound(alert_type)
            
            # استدعاء callbacks المسجلة
            for callback in self.notification_callbacks:
                try:
                    callback(alert)
                except Exception as e:
                    logger.warning(f"خطأ في استدعاء callback: {e}")
            
            logger.info(f"تم عرض تنبيه: {alert_message}")
            
        except Exception as e:
            logger.error(f"خطأ في عرض التنبيه: {e}")
    
    def show_desktop_notification(self, title: str, message: str, icon_type: str = 'info'):
        """عرض إشعار سطح المكتب"""
        try:
            if not PLYER_AVAILABLE:
                return
            
            # تحديد الأيقونة
            app_icon = None
            if os.path.exists("assets/icon.ico"):
                app_icon = "assets/icon.ico"
            
            notification.notify(
                title=title,
                message=message,
                app_name="برنامج ضغط وسكر",
                app_icon=app_icon,
                timeout=10
            )
            
        except Exception as e:
            logger.warning(f"تحذير في عرض إشعار سطح المكتب: {e}")
    
    def show_popup_alert(self, title: str, message: str, icon_type: str = 'info'):
        """عرض نافذة تنبيه منبثقة"""
        try:
            if not self.root_window:
                return
            
            # التأكد من أن النافذة في المقدمة
            def show_alert():
                self.root_window.lift()
                self.root_window.attributes('-topmost', True)
                self.root_window.after_idle(self.root_window.attributes, '-topmost', False)
                
                if icon_type == 'warning':
                    messagebox.showwarning(title, message)
                elif icon_type == 'error':
                    messagebox.showerror(title, message)
                else:
                    messagebox.showinfo(title, message)
            
            # تشغيل في الخيط الرئيسي
            self.root_window.after(0, show_alert)
            
        except Exception as e:
            logger.warning(f"تحذير في عرض النافذة المنبثقة: {e}")
    
    def play_alert_sound(self, alert_type: str):
        """تشغيل صوت التنبيه"""
        try:
            # يمكن إضافة أصوات مختلفة لأنواع التنبيهات المختلفة
            # هنا نستخدم صوت النظام الافتراضي
            if os.name == 'nt':  # Windows
                import winsound
                if alert_type in ['high_blood_pressure', 'high_blood_sugar']:
                    winsound.MessageBeep(winsound.MB_ICONEXCLAMATION)
                else:
                    winsound.MessageBeep(winsound.MB_ICONASTERISK)
            else:
                # Linux/Mac - يمكن استخدام مكتبات أخرى
                print('\a')  # صوت بيب بسيط
                
        except Exception as e:
            logger.warning(f"تحذير في تشغيل صوت التنبيه: {e}")
    
    def show_measurement_reminder(self, user: Dict):
        """عرض تذكير بتسجيل القياسات"""
        try:
            message = f"تذكير: لم تسجل قياساتك اليوم يا {user['name']}!\nلا تنس تسجيل قياسات ضغط الدم والسكر."
            
            self.show_desktop_notification("تذكير القياسات", message)
            
            if self.root_window:
                def show_reminder():
                    result = messagebox.askyesno(
                        "تذكير القياسات",
                        message + "\n\nهل تريد تسجيل قياس جديد الآن؟"
                    )
                    if result:
                        # يمكن إضافة استدعاء لفتح نافذة القياس الجديد
                        pass
                
                self.root_window.after(0, show_reminder)
            
        except Exception as e:
            logger.error(f"خطأ في عرض تذكير القياسات: {e}")
    
    def add_notification_callback(self, callback: Callable):
        """إضافة callback لاستقبال التنبيهات"""
        if callback not in self.notification_callbacks:
            self.notification_callbacks.append(callback)
    
    def remove_notification_callback(self, callback: Callable):
        """إزالة callback"""
        if callback in self.notification_callbacks:
            self.notification_callbacks.remove(callback)
    
    def create_custom_alert(self, user_id: int, alert_type: str, message: str):
        """إنشاء تنبيه مخصص"""
        try:
            current_date = date.today()
            current_time = datetime.now().strftime('%H:%M')
            
            # إضافة التنبيه إلى قاعدة البيانات
            self.db_manager.cursor.execute('''
                INSERT INTO alerts (user_id, alert_type, message, alert_date, alert_time)
                VALUES (?, ?, ?, ?, ?)
            ''', (user_id, alert_type, message, current_date, current_time))
            
            self.db_manager.connection.commit()
            
            # عرض التنبيه فوراً
            alert = {
                'id': self.db_manager.cursor.lastrowid,
                'user_id': user_id,
                'alert_type': alert_type,
                'message': message,
                'alert_date': current_date,
                'alert_time': current_time,
                'is_read': False
            }
            
            self.show_alert(alert)
            logger.info(f"تم إنشاء تنبيه مخصص: {message}")
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء التنبيه المخصص: {e}")
    
    def mark_alert_as_read(self, alert_id: int):
        """تحديد التنبيه كمقروء"""
        try:
            self.db_manager.mark_alert_as_read(alert_id)
            
            # إزالة من القائمة النشطة
            self.active_alerts = [a for a in self.active_alerts if a['id'] != alert_id]
            
        except Exception as e:
            logger.error(f"خطأ في تحديد التنبيه كمقروء: {e}")
    
    def get_alert_statistics(self) -> Dict:
        """الحصول على إحصائيات التنبيهات"""
        try:
            # إحصائيات التنبيهات خلال الشهر الماضي
            start_date = date.today() - timedelta(days=30)
            
            self.db_manager.cursor.execute('''
                SELECT alert_type, COUNT(*) as count
                FROM alerts
                WHERE alert_date >= ?
                GROUP BY alert_type
            ''', (start_date,))
            
            results = self.db_manager.cursor.fetchall()
            
            stats = {
                'total_alerts': sum(row['count'] for row in results),
                'by_type': {row['alert_type']: row['count'] for row in results},
                'period_days': 30
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"خطأ في جلب إحصائيات التنبيهات: {e}")
            return {}
    
    def update_settings(self, new_settings: Dict):
        """تحديث إعدادات التنبيهات"""
        try:
            self.settings.update(new_settings)
            self.save_settings()
            
            # إعادة تشغيل المراقبة إذا تم تفعيلها
            if self.settings['enabled'] and not self.is_running:
                self.start_monitoring()
            elif not self.settings['enabled'] and self.is_running:
                self.stop_monitoring()
            
            logger.info("تم تحديث إعدادات التنبيهات")
            
        except Exception as e:
            logger.error(f"خطأ في تحديث إعدادات التنبيهات: {e}")
    
    def get_settings(self) -> Dict:
        """الحصول على إعدادات التنبيهات الحالية"""
        return self.settings.copy()

class AlertDialog:
    """نافذة حوار التنبيهات"""
    
    def __init__(self, parent, alert: Dict):
        """
        إنشاء نافذة تنبيه
        
        Args:
            parent: النافذة الأب
            alert: بيانات التنبيه
        """
        self.alert = alert
        self.result = None
        
        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("تنبيه صحي")
        self.dialog.geometry("400x200")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # وضع النافذة في المنتصف
        self.dialog.geometry("+%d+%d" % (
            parent.winfo_rootx() + 50,
            parent.winfo_rooty() + 50
        ))
        
        self.create_widgets()
    
    def create_widgets(self):
        """إنشاء عناصر النافذة"""
        # إطار المحتوى
        content_frame = tk.Frame(self.dialog, padx=20, pady=20)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # رسالة التنبيه
        message_label = tk.Label(
            content_frame,
            text=self.alert['message'],
            font=("Arial", 12),
            wraplength=350,
            justify=tk.RIGHT
        )
        message_label.pack(pady=(0, 20))
        
        # تاريخ ووقت التنبيه
        datetime_label = tk.Label(
            content_frame,
            text=f"التاريخ: {self.alert['alert_date']} - الوقت: {self.alert['alert_time']}",
            font=("Arial", 10),
            fg="gray"
        )
        datetime_label.pack(pady=(0, 20))
        
        # أزرار
        buttons_frame = tk.Frame(content_frame)
        buttons_frame.pack()
        
        ok_button = tk.Button(
            buttons_frame,
            text="موافق",
            command=self.ok_clicked,
            width=10
        )
        ok_button.pack(side=tk.LEFT, padx=5)
        
        details_button = tk.Button(
            buttons_frame,
            text="التفاصيل",
            command=self.details_clicked,
            width=10
        )
        details_button.pack(side=tk.LEFT, padx=5)
    
    def ok_clicked(self):
        """عند النقر على موافق"""
        self.result = "ok"
        self.dialog.destroy()
    
    def details_clicked(self):
        """عند النقر على التفاصيل"""
        self.result = "details"
        self.dialog.destroy()
    
    def show(self):
        """عرض النافذة وانتظار النتيجة"""
        self.dialog.wait_window()
        return self.result
