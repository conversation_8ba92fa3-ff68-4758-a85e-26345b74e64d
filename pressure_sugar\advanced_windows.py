#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
النوافذ المتقدمة
Advanced Windows Module

تحتوي على النوافذ المتقدمة للإعدادات والتقارير والرسوم البيانية
مع واجهات تفاعلية وخيارات تخصيص شاملة

المطور: مساعد الذكي الاصطناعي
التاريخ: 2025-06-21
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, colorchooser
from datetime import datetime, date, timedelta
import logging
from typing import Optional, Dict, List, Tuple
import os
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
import matplotlib.dates as mdates

logger = logging.getLogger(__name__)

class SettingsWindow:
    """نافذة الإعدادات المتقدمة"""

    def __init__(self, parent, db_manager, notification_manager=None):
        """
        إنشاء نافذة الإعدادات

        Args:
            parent: النافذة الأب
            db_manager: مدير قاعدة البيانات
            notification_manager: مدير التنبيهات
        """
        self.parent = parent
        self.db_manager = db_manager
        self.notification_manager = notification_manager
        self.result = None

        # إنشاء النافذة
        self.window = tk.Toplevel(parent)
        self.window.title("الإعدادات")
        self.window.geometry("600x700")
        self.window.resizable(True, True)
        self.window.transient(parent)
        self.window.grab_set()

        # وضع النافذة في المنتصف
        self.center_window()

        # متغيرات الإعدادات
        self.setup_variables()

        # تحميل الإعدادات الحالية
        self.load_current_settings()

        self.create_widgets()

    def center_window(self):
        """وضع النافذة في منتصف الشاشة"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (600 // 2)
        y = (self.window.winfo_screenheight() // 2) - (700 // 2)
        self.window.geometry(f"600x700+{x}+{y}")

    def setup_variables(self):
        """إعداد متغيرات النموذج"""
        # إعدادات التنبيهات
        self.notifications_enabled = tk.BooleanVar(value=True)
        self.desktop_notifications = tk.BooleanVar(value=True)
        self.popup_alerts = tk.BooleanVar(value=True)
        self.sound_enabled = tk.BooleanVar(value=True)
        self.medication_reminders = tk.BooleanVar(value=True)
        self.measurement_reminders = tk.BooleanVar(value=True)

        # الحدود الطبيعية - ضغط الدم
        self.normal_systolic_max = tk.StringVar(value="120")
        self.normal_diastolic_max = tk.StringVar(value="80")
        self.high_systolic_min = tk.StringVar(value="140")
        self.high_diastolic_min = tk.StringVar(value="90")

        # الحدود الطبيعية - السكر
        self.normal_sugar_fasting_max = tk.StringVar(value="100")
        self.normal_sugar_after_meal_max = tk.StringVar(value="140")
        self.high_sugar_min = tk.StringVar(value="180")
        self.low_sugar_min = tk.StringVar(value="70")

        # إعدادات المظهر
        self.app_language = tk.StringVar(value="ar")
        self.theme_color = tk.StringVar(value="#2E86AB")
        self.font_size = tk.StringVar(value="12")

        # إعدادات النسخ الاحتياطي
        self.backup_enabled = tk.BooleanVar(value=True)
        self.backup_interval = tk.StringVar(value="7")  # أيام
        self.backup_location = tk.StringVar(value="backups/")

    def load_current_settings(self):
        """تحميل الإعدادات الحالية من قاعدة البيانات"""
        try:
            # تحميل إعدادات التنبيهات
            notifications_enabled = self.db_manager.get_setting('notifications_enabled')
            if notifications_enabled:
                self.notifications_enabled.set(notifications_enabled == '1')

            # تحميل الحدود الطبيعية
            settings_map = {
                'normal_systolic_max': self.normal_systolic_max,
                'normal_diastolic_max': self.normal_diastolic_max,
                'high_systolic_min': self.high_systolic_min,
                'high_diastolic_min': self.high_diastolic_min,
                'normal_sugar_fasting_max': self.normal_sugar_fasting_max,
                'normal_sugar_after_meal_max': self.normal_sugar_after_meal_max,
                'high_sugar_min': self.high_sugar_min,
                'app_language': self.app_language,
                'backup_enabled': self.backup_enabled
            }

            for setting_key, var in settings_map.items():
                value = self.db_manager.get_setting(setting_key)
                if value:
                    if isinstance(var, tk.BooleanVar):
                        var.set(value == '1')
                    else:
                        var.set(value)

            # تحميل إعدادات مدير التنبيهات
            if self.notification_manager:
                settings = self.notification_manager.get_settings()
                self.desktop_notifications.set(settings.get('show_desktop_notifications', True))
                self.popup_alerts.set(settings.get('show_popup_alerts', True))
                self.sound_enabled.set(settings.get('sound_enabled', True))
                self.medication_reminders.set(settings.get('medication_reminders', True))
                self.measurement_reminders.set(settings.get('measurement_reminders', True))

        except Exception as e:
            logger.warning(f"تحذير في تحميل الإعدادات: {e}")

    def create_widgets(self):
        """إنشاء عناصر النافذة"""
        # إطار رئيسي مع شريط تمرير
        main_frame = tk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # إنشاء Canvas وScrollbar
        canvas = tk.Canvas(main_frame)
        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # عنوان
        title_label = tk.Label(
            scrollable_frame,
            text="الإعدادات",
            font=("Arial", 18, "bold"),
            fg="#2E86AB"
        )
        title_label.pack(pady=(0, 20))

        # إنشاء التبويبات
        notebook = ttk.Notebook(scrollable_frame)
        notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        # تبويب التنبيهات
        self.create_notifications_tab(notebook)

        # تبويب الحدود الطبيعية
        self.create_limits_tab(notebook)

        # تبويب المظهر
        self.create_appearance_tab(notebook)

        # تبويب النسخ الاحتياطي
        self.create_backup_tab(notebook)

        # أزرار
        buttons_frame = tk.Frame(scrollable_frame)
        buttons_frame.pack(fill=tk.X, pady=(10, 0))

        # زر الافتراضي
        default_button = tk.Button(
            buttons_frame,
            text="الإعدادات الافتراضية",
            command=self.reset_to_defaults,
            font=("Arial", 11),
            bg="#F18F01",
            fg="white",
            width=15
        )
        default_button.pack(side=tk.LEFT)

        # زر الإلغاء
        cancel_button = tk.Button(
            buttons_frame,
            text="إلغاء",
            command=self.cancel_clicked,
            font=("Arial", 11),
            bg="#C73E1D",
            fg="white",
            width=12
        )
        cancel_button.pack(side=tk.RIGHT, padx=(10, 0))

        # زر الحفظ
        save_button = tk.Button(
            buttons_frame,
            text="حفظ",
            command=self.save_clicked,
            font=("Arial", 11),
            bg="#2E86AB",
            fg="white",
            width=12
        )
        save_button.pack(side=tk.RIGHT)

        # تخطيط Canvas وScrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # ربط عجلة الماوس
        def _on_mousewheel(event):
            try:
                canvas.yview_scroll(int(-1*(event.delta/120)), "units")
            except tk.TclError:
                pass  # تجاهل الخطأ إذا تم إغلاق النافذة

        def _bind_mousewheel(event):
            canvas.bind_all("<MouseWheel>", _on_mousewheel)

        def _unbind_mousewheel(event):
            canvas.unbind_all("<MouseWheel>")

        # ربط وإلغاء ربط عجلة الماوس عند دخول وخروج النافذة
        self.window.bind('<Enter>', _bind_mousewheel)
        self.window.bind('<Leave>', _unbind_mousewheel)

        # تنظيف عند إغلاق النافذة
        def on_closing():
            try:
                canvas.unbind_all("<MouseWheel>")
            except:
                pass
            self.window.destroy()

        self.window.protocol("WM_DELETE_WINDOW", on_closing)

    def create_notifications_tab(self, notebook):
        """إنشاء تبويب التنبيهات"""
        notifications_frame = ttk.Frame(notebook)
        notebook.add(notifications_frame, text="التنبيهات")

        # إطار المحتوى
        content_frame = tk.Frame(notifications_frame, padx=20, pady=20)
        content_frame.pack(fill=tk.BOTH, expand=True)

        # تفعيل التنبيهات
        tk.Label(content_frame, text="إعدادات التنبيهات العامة",
                font=("Arial", 14, "bold")).pack(anchor=tk.W, pady=(0, 10))

        tk.Checkbutton(
            content_frame,
            text="تفعيل التنبيهات",
            variable=self.notifications_enabled,
            font=("Arial", 12),
            command=self.toggle_notifications
        ).pack(anchor=tk.W, pady=5)

        # فاصل
        ttk.Separator(content_frame, orient='horizontal').pack(fill=tk.X, pady=15)

        # أنواع التنبيهات
        tk.Label(content_frame, text="أنواع التنبيهات",
                font=("Arial", 14, "bold")).pack(anchor=tk.W, pady=(0, 10))

        self.desktop_check = tk.Checkbutton(
            content_frame,
            text="إشعارات سطح المكتب",
            variable=self.desktop_notifications,
            font=("Arial", 12)
        )
        self.desktop_check.pack(anchor=tk.W, pady=5)

        self.popup_check = tk.Checkbutton(
            content_frame,
            text="النوافذ المنبثقة",
            variable=self.popup_alerts,
            font=("Arial", 12)
        )
        self.popup_check.pack(anchor=tk.W, pady=5)

        self.sound_check = tk.Checkbutton(
            content_frame,
            text="الأصوات",
            variable=self.sound_enabled,
            font=("Arial", 12)
        )
        self.sound_check.pack(anchor=tk.W, pady=5)

        # فاصل
        ttk.Separator(content_frame, orient='horizontal').pack(fill=tk.X, pady=15)

        # التذكيرات
        tk.Label(content_frame, text="التذكيرات",
                font=("Arial", 14, "bold")).pack(anchor=tk.W, pady=(0, 10))

        self.medication_check = tk.Checkbutton(
            content_frame,
            text="تذكيرات الأدوية",
            variable=self.medication_reminders,
            font=("Arial", 12)
        )
        self.medication_check.pack(anchor=tk.W, pady=5)

        self.measurement_check = tk.Checkbutton(
            content_frame,
            text="تذكيرات القياسات",
            variable=self.measurement_reminders,
            font=("Arial", 12)
        )
        self.measurement_check.pack(anchor=tk.W, pady=5)

        # تطبيق الحالة الأولية
        self.toggle_notifications()

    def toggle_notifications(self):
        """تفعيل/إلغاء تفعيل التنبيهات"""
        enabled = self.notifications_enabled.get()

        widgets = [
            self.desktop_check, self.popup_check, self.sound_check,
            self.medication_check, self.measurement_check
        ]

        for widget in widgets:
            widget.configure(state=tk.NORMAL if enabled else tk.DISABLED)

    def create_limits_tab(self, notebook):
        """إنشاء تبويب الحدود الطبيعية"""
        limits_frame = ttk.Frame(notebook)
        notebook.add(limits_frame, text="الحدود الطبيعية")

        # إطار المحتوى
        content_frame = tk.Frame(limits_frame, padx=20, pady=20)
        content_frame.pack(fill=tk.BOTH, expand=True)

        # ضغط الدم
        bp_frame = tk.LabelFrame(content_frame, text="ضغط الدم (mmHg)",
                                font=("Arial", 14, "bold"))
        bp_frame.pack(fill=tk.X, pady=(0, 20))

        bp_inner = tk.Frame(bp_frame, padx=15, pady=15)
        bp_inner.pack(fill=tk.X)

        # الحد الأقصى الطبيعي
        normal_frame = tk.Frame(bp_inner)
        normal_frame.pack(fill=tk.X, pady=5)

        tk.Label(normal_frame, text="الحد الأقصى الطبيعي:",
                font=("Arial", 12)).pack(side=tk.LEFT)

        tk.Entry(normal_frame, textvariable=self.normal_systolic_max,
                width=8, font=("Arial", 11)).pack(side=tk.RIGHT, padx=(5, 0))
        tk.Label(normal_frame, text="/", font=("Arial", 12)).pack(side=tk.RIGHT)
        tk.Entry(normal_frame, textvariable=self.normal_diastolic_max,
                width=8, font=("Arial", 11)).pack(side=tk.RIGHT)

        # الحد الأدنى للارتفاع
        high_frame = tk.Frame(bp_inner)
        high_frame.pack(fill=tk.X, pady=5)

        tk.Label(high_frame, text="الحد الأدنى للارتفاع:",
                font=("Arial", 12)).pack(side=tk.LEFT)

        tk.Entry(high_frame, textvariable=self.high_systolic_min,
                width=8, font=("Arial", 11)).pack(side=tk.RIGHT, padx=(5, 0))
        tk.Label(high_frame, text="/", font=("Arial", 12)).pack(side=tk.RIGHT)
        tk.Entry(high_frame, textvariable=self.high_diastolic_min,
                width=8, font=("Arial", 11)).pack(side=tk.RIGHT)

        # السكر
        sugar_frame = tk.LabelFrame(content_frame, text="مستوى السكر (mg/dL)",
                                   font=("Arial", 14, "bold"))
        sugar_frame.pack(fill=tk.X, pady=(0, 20))

        sugar_inner = tk.Frame(sugar_frame, padx=15, pady=15)
        sugar_inner.pack(fill=tk.X)

        # السكر الصائم
        fasting_frame = tk.Frame(sugar_inner)
        fasting_frame.pack(fill=tk.X, pady=5)

        tk.Label(fasting_frame, text="الحد الأقصى للصائم:",
                font=("Arial", 12)).pack(side=tk.LEFT)
        tk.Entry(fasting_frame, textvariable=self.normal_sugar_fasting_max,
                width=10, font=("Arial", 11)).pack(side=tk.RIGHT)

        # السكر بعد الأكل
        after_meal_frame = tk.Frame(sugar_inner)
        after_meal_frame.pack(fill=tk.X, pady=5)

        tk.Label(after_meal_frame, text="الحد الأقصى بعد الأكل:",
                font=("Arial", 12)).pack(side=tk.LEFT)
        tk.Entry(after_meal_frame, textvariable=self.normal_sugar_after_meal_max,
                width=10, font=("Arial", 11)).pack(side=tk.RIGHT)

        # الحد الأدنى للارتفاع
        high_sugar_frame = tk.Frame(sugar_inner)
        high_sugar_frame.pack(fill=tk.X, pady=5)

        tk.Label(high_sugar_frame, text="الحد الأدنى للارتفاع:",
                font=("Arial", 12)).pack(side=tk.LEFT)
        tk.Entry(high_sugar_frame, textvariable=self.high_sugar_min,
                width=10, font=("Arial", 11)).pack(side=tk.RIGHT)

        # الحد الأدنى للانخفاض
        low_sugar_frame = tk.Frame(sugar_inner)
        low_sugar_frame.pack(fill=tk.X, pady=5)

        tk.Label(low_sugar_frame, text="الحد الأدنى للانخفاض:",
                font=("Arial", 12)).pack(side=tk.LEFT)
        tk.Entry(low_sugar_frame, textvariable=self.low_sugar_min,
                width=10, font=("Arial", 11)).pack(side=tk.RIGHT)

    def create_appearance_tab(self, notebook):
        """إنشاء تبويب المظهر"""
        appearance_frame = ttk.Frame(notebook)
        notebook.add(appearance_frame, text="المظهر")

        # إطار المحتوى
        content_frame = tk.Frame(appearance_frame, padx=20, pady=20)
        content_frame.pack(fill=tk.BOTH, expand=True)

        # اللغة
        lang_frame = tk.LabelFrame(content_frame, text="اللغة",
                                  font=("Arial", 14, "bold"))
        lang_frame.pack(fill=tk.X, pady=(0, 20))

        lang_inner = tk.Frame(lang_frame, padx=15, pady=15)
        lang_inner.pack(fill=tk.X)

        tk.Radiobutton(lang_inner, text="العربية", variable=self.app_language,
                      value="ar", font=("Arial", 12)).pack(anchor=tk.W)
        tk.Radiobutton(lang_inner, text="English", variable=self.app_language,
                      value="en", font=("Arial", 12)).pack(anchor=tk.W)

        # الألوان
        colors_frame = tk.LabelFrame(content_frame, text="الألوان",
                                    font=("Arial", 14, "bold"))
        colors_frame.pack(fill=tk.X, pady=(0, 20))

        colors_inner = tk.Frame(colors_frame, padx=15, pady=15)
        colors_inner.pack(fill=tk.X)

        theme_frame = tk.Frame(colors_inner)
        theme_frame.pack(fill=tk.X, pady=5)

        tk.Label(theme_frame, text="لون الثيم:",
                font=("Arial", 12)).pack(side=tk.LEFT)

        color_button = tk.Button(
            theme_frame,
            text="اختيار اللون",
            command=self.choose_theme_color,
            font=("Arial", 11),
            width=12
        )
        color_button.pack(side=tk.RIGHT)

        # الخط
        font_frame = tk.LabelFrame(content_frame, text="الخط",
                                  font=("Arial", 14, "bold"))
        font_frame.pack(fill=tk.X, pady=(0, 20))

        font_inner = tk.Frame(font_frame, padx=15, pady=15)
        font_inner.pack(fill=tk.X)

        size_frame = tk.Frame(font_inner)
        size_frame.pack(fill=tk.X, pady=5)

        tk.Label(size_frame, text="حجم الخط:",
                font=("Arial", 12)).pack(side=tk.LEFT)

        font_combo = ttk.Combobox(
            size_frame,
            textvariable=self.font_size,
            values=["10", "11", "12", "13", "14", "15", "16"],
            width=8,
            state="readonly"
        )
        font_combo.pack(side=tk.RIGHT)

    def create_backup_tab(self, notebook):
        """إنشاء تبويب النسخ الاحتياطي"""
        backup_frame = ttk.Frame(notebook)
        notebook.add(backup_frame, text="النسخ الاحتياطي")

        # إطار المحتوى
        content_frame = tk.Frame(backup_frame, padx=20, pady=20)
        content_frame.pack(fill=tk.BOTH, expand=True)

        # تفعيل النسخ الاحتياطي
        tk.Checkbutton(
            content_frame,
            text="تفعيل النسخ الاحتياطي التلقائي",
            variable=self.backup_enabled,
            font=("Arial", 12)
        ).pack(anchor=tk.W, pady=(0, 20))

        # فترة النسخ الاحتياطي
        interval_frame = tk.Frame(content_frame)
        interval_frame.pack(fill=tk.X, pady=(0, 20))

        tk.Label(interval_frame, text="فترة النسخ الاحتياطي (أيام):",
                font=("Arial", 12)).pack(side=tk.LEFT)

        interval_combo = ttk.Combobox(
            interval_frame,
            textvariable=self.backup_interval,
            values=["1", "3", "7", "14", "30"],
            width=8,
            state="readonly"
        )
        interval_combo.pack(side=tk.RIGHT)

        # مكان النسخ الاحتياطي
        location_frame = tk.Frame(content_frame)
        location_frame.pack(fill=tk.X, pady=(0, 20))

        tk.Label(location_frame, text="مكان النسخ الاحتياطي:",
                font=("Arial", 12)).pack(anchor=tk.W)

        location_entry_frame = tk.Frame(location_frame)
        location_entry_frame.pack(fill=tk.X, pady=(5, 0))

        tk.Entry(location_entry_frame, textvariable=self.backup_location,
                font=("Arial", 11)).pack(side=tk.LEFT, fill=tk.X, expand=True)

        browse_button = tk.Button(
            location_entry_frame,
            text="تصفح",
            command=self.browse_backup_location,
            font=("Arial", 11),
            width=8
        )
        browse_button.pack(side=tk.RIGHT, padx=(5, 0))

        # أزرار النسخ الاحتياطي
        backup_buttons_frame = tk.Frame(content_frame)
        backup_buttons_frame.pack(fill=tk.X, pady=(20, 0))

        create_backup_button = tk.Button(
            backup_buttons_frame,
            text="إنشاء نسخة احتياطية الآن",
            command=self.create_backup_now,
            font=("Arial", 11),
            bg="#F18F01",
            fg="white",
            width=20
        )
        create_backup_button.pack(side=tk.LEFT)

        restore_button = tk.Button(
            backup_buttons_frame,
            text="استعادة من نسخة احتياطية",
            command=self.restore_from_backup,
            font=("Arial", 11),
            bg="#A23B72",
            fg="white",
            width=20
        )
        restore_button.pack(side=tk.RIGHT)

    def choose_theme_color(self):
        """اختيار لون الثيم"""
        try:
            color = colorchooser.askcolor(
                title="اختيار لون الثيم",
                initialcolor=self.theme_color.get()
            )
            if color[1]:  # إذا تم اختيار لون
                self.theme_color.set(color[1])
        except Exception as e:
            logger.warning(f"تحذير في اختيار اللون: {e}")

    def browse_backup_location(self):
        """تصفح مكان النسخ الاحتياطي"""
        try:
            directory = filedialog.askdirectory(
                title="اختيار مجلد النسخ الاحتياطي",
                initialdir=self.backup_location.get()
            )
            if directory:
                self.backup_location.set(directory + "/")
        except Exception as e:
            logger.warning(f"تحذير في تصفح المجلد: {e}")

    def create_backup_now(self):
        """إنشاء نسخة احتياطية فورية"""
        try:
            import shutil
            from datetime import datetime

            # إنشاء مجلد النسخ الاحتياطي
            backup_dir = self.backup_location.get()
            os.makedirs(backup_dir, exist_ok=True)

            # اسم النسخة الاحتياطية
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"backup_{timestamp}"
            backup_path = os.path.join(backup_dir, backup_name)

            # نسخ مجلد البيانات
            if os.path.exists("data"):
                shutil.copytree("data", os.path.join(backup_path, "data"))

            # نسخ مجلد التقارير
            if os.path.exists("reports"):
                shutil.copytree("reports", os.path.join(backup_path, "reports"))

            messagebox.showinfo("نجح", f"تم إنشاء النسخة الاحتياطية بنجاح:\n{backup_path}")

        except Exception as e:
            logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            messagebox.showerror("خطأ", f"فشل في إنشاء النسخة الاحتياطية:\n{e}")

    def restore_from_backup(self):
        """استعادة من نسخة احتياطية"""
        try:
            backup_dir = filedialog.askdirectory(
                title="اختيار مجلد النسخة الاحتياطية"
            )

            if not backup_dir:
                return

            # التحقق من وجود البيانات
            data_path = os.path.join(backup_dir, "data")
            if not os.path.exists(data_path):
                messagebox.showerror("خطأ", "النسخة الاحتياطية غير صالحة")
                return

            # تأكيد الاستعادة
            result = messagebox.askyesno(
                "تأكيد الاستعادة",
                "هذا سيحل محل البيانات الحالية.\nهل أنت متأكد؟"
            )

            if result:
                import shutil

                # نسخ احتياطية للبيانات الحالية
                if os.path.exists("data"):
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    shutil.move("data", f"data_backup_{timestamp}")

                # استعادة البيانات
                shutil.copytree(data_path, "data")

                # استعادة التقارير إذا كانت موجودة
                reports_path = os.path.join(backup_dir, "reports")
                if os.path.exists(reports_path):
                    if os.path.exists("reports"):
                        shutil.rmtree("reports")
                    shutil.copytree(reports_path, "reports")

                messagebox.showinfo("نجح", "تم استعادة البيانات بنجاح\nسيتم إعادة تشغيل البرنامج")

                # إعادة تشغيل البرنامج
                self.window.quit()

        except Exception as e:
            logger.error(f"خطأ في استعادة النسخة الاحتياطية: {e}")
            messagebox.showerror("خطأ", f"فشل في استعادة النسخة الاحتياطية:\n{e}")

    def reset_to_defaults(self):
        """إعادة تعيين الإعدادات الافتراضية"""
        try:
            result = messagebox.askyesno(
                "تأكيد الإعادة",
                "هذا سيعيد جميع الإعدادات إلى القيم الافتراضية.\nهل أنت متأكد؟"
            )

            if result:
                # إعادة تعيين التنبيهات
                self.notifications_enabled.set(True)
                self.desktop_notifications.set(True)
                self.popup_alerts.set(True)
                self.sound_enabled.set(True)
                self.medication_reminders.set(True)
                self.measurement_reminders.set(True)

                # إعادة تعيين الحدود الطبيعية
                self.normal_systolic_max.set("120")
                self.normal_diastolic_max.set("80")
                self.high_systolic_min.set("140")
                self.high_diastolic_min.set("90")
                self.normal_sugar_fasting_max.set("100")
                self.normal_sugar_after_meal_max.set("140")
                self.high_sugar_min.set("180")
                self.low_sugar_min.set("70")

                # إعادة تعيين المظهر
                self.app_language.set("ar")
                self.theme_color.set("#2E86AB")
                self.font_size.set("12")

                # إعادة تعيين النسخ الاحتياطي
                self.backup_enabled.set(True)
                self.backup_interval.set("7")
                self.backup_location.set("backups/")

                messagebox.showinfo("تم", "تم إعادة تعيين الإعدادات الافتراضية")

        except Exception as e:
            logger.error(f"خطأ في إعادة التعيين: {e}")

    def save_clicked(self):
        """عند النقر على حفظ"""
        try:
            # التحقق من صحة البيانات
            if not self.validate_settings():
                return

            # حفظ الإعدادات في قاعدة البيانات
            settings_to_save = {
                'notifications_enabled': '1' if self.notifications_enabled.get() else '0',
                'normal_systolic_max': self.normal_systolic_max.get(),
                'normal_diastolic_max': self.normal_diastolic_max.get(),
                'high_systolic_min': self.high_systolic_min.get(),
                'high_diastolic_min': self.high_diastolic_min.get(),
                'normal_sugar_fasting_max': self.normal_sugar_fasting_max.get(),
                'normal_sugar_after_meal_max': self.normal_sugar_after_meal_max.get(),
                'high_sugar_min': self.high_sugar_min.get(),
                'app_language': self.app_language.get(),
                'backup_enabled': '1' if self.backup_enabled.get() else '0'
            }

            for key, value in settings_to_save.items():
                self.db_manager.set_setting(key, value)

            # تحديث إعدادات مدير التنبيهات
            if self.notification_manager:
                notification_settings = {
                    'enabled': self.notifications_enabled.get(),
                    'show_desktop_notifications': self.desktop_notifications.get(),
                    'show_popup_alerts': self.popup_alerts.get(),
                    'sound_enabled': self.sound_enabled.get(),
                    'medication_reminders': self.medication_reminders.get(),
                    'measurement_reminders': self.measurement_reminders.get()
                }

                self.notification_manager.update_settings(notification_settings)

            self.result = True
            messagebox.showinfo("نجح", "تم حفظ الإعدادات بنجاح")
            self.window.destroy()

        except Exception as e:
            logger.error(f"خطأ في حفظ الإعدادات: {e}")
            messagebox.showerror("خطأ", f"فشل في حفظ الإعدادات:\n{e}")

    def validate_settings(self):
        """التحقق من صحة الإعدادات"""
        try:
            # التحقق من الحدود الطبيعية لضغط الدم
            normal_sys = int(self.normal_systolic_max.get())
            normal_dia = int(self.normal_diastolic_max.get())
            high_sys = int(self.high_systolic_min.get())
            high_dia = int(self.high_diastolic_min.get())

            if normal_sys >= high_sys or normal_dia >= high_dia:
                messagebox.showerror("خطأ", "الحد الأقصى الطبيعي يجب أن يكون أقل من الحد الأدنى للارتفاع")
                return False

            # التحقق من حدود السكر
            normal_fasting = float(self.normal_sugar_fasting_max.get())
            normal_after_meal = float(self.normal_sugar_after_meal_max.get())
            high_sugar = float(self.high_sugar_min.get())
            low_sugar = float(self.low_sugar_min.get())

            if normal_fasting >= high_sugar or normal_after_meal >= high_sugar:
                messagebox.showerror("خطأ", "الحد الأقصى الطبيعي للسكر يجب أن يكون أقل من الحد الأدنى للارتفاع")
                return False

            if low_sugar >= normal_fasting:
                messagebox.showerror("خطأ", "الحد الأدنى للانخفاض يجب أن يكون أقل من الحد الأقصى الطبيعي")
                return False

            return True

        except ValueError:
            messagebox.showerror("خطأ", "يجب إدخال قيم رقمية صحيحة")
            return False
        except Exception as e:
            logger.error(f"خطأ في التحقق من الإعدادات: {e}")
            return False

    def cancel_clicked(self):
        """عند النقر على إلغاء"""
        self.result = False
        self.window.destroy()

    def show(self):
        """عرض النافذة وانتظار النتيجة"""
        self.window.wait_window()
        return self.result

class ReportsWindow:
    """نافذة التقارير التفاعلية"""

    def __init__(self, parent, db_manager, data_analyzer, report_exporter):
        """
        إنشاء نافذة التقارير

        Args:
            parent: النافذة الأب
            db_manager: مدير قاعدة البيانات
            data_analyzer: محلل البيانات
            report_exporter: مصدر التقارير
        """
        self.parent = parent
        self.db_manager = db_manager
        self.data_analyzer = data_analyzer
        self.report_exporter = report_exporter

        # إنشاء النافذة
        self.window = tk.Toplevel(parent)
        self.window.title("التقارير")
        self.window.geometry("800x600")
        self.window.resizable(True, True)
        self.window.transient(parent)
        self.window.grab_set()

        # وضع النافذة في المنتصف
        self.center_window()

        # متغيرات النموذج
        self.setup_variables()

        # تحميل المستخدمين
        self.users = self.db_manager.get_users()

        self.create_widgets()

        # تحديث قائمة التقارير
        self.refresh_reports_list()

    def center_window(self):
        """وضع النافذة في منتصف الشاشة"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (800 // 2)
        y = (self.window.winfo_screenheight() // 2) - (600 // 2)
        self.window.geometry(f"800x600+{x}+{y}")

    def setup_variables(self):
        """إعداد متغيرات النموذج"""
        self.selected_user = tk.StringVar()
        self.start_date = tk.StringVar(value=(date.today() - timedelta(days=30)).strftime("%Y-%m-%d"))
        self.end_date = tk.StringVar(value=date.today().strftime("%Y-%m-%d"))
        self.report_type = tk.StringVar(value="comprehensive")
        self.export_format = tk.StringVar(value="pdf")
        self.include_charts = tk.BooleanVar(value=True)
        self.include_statistics = tk.BooleanVar(value=True)
        self.include_recommendations = tk.BooleanVar(value=True)

    def create_widgets(self):
        """إنشاء عناصر النافذة"""
        # إطار رئيسي
        main_frame = tk.Frame(self.window, padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # عنوان
        title_label = tk.Label(
            main_frame,
            text="إنشاء وإدارة التقارير",
            font=("Arial", 18, "bold"),
            fg="#2E86AB"
        )
        title_label.pack(pady=(0, 20))

        # إنشاء التبويبات
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True)

        # تبويب إنشاء تقرير جديد
        self.create_new_report_tab(notebook)

        # تبويب التقارير المحفوظة
        self.create_saved_reports_tab(notebook)

    def create_new_report_tab(self, notebook):
        """إنشاء تبويب التقرير الجديد"""
        new_report_frame = ttk.Frame(notebook)
        notebook.add(new_report_frame, text="تقرير جديد")

        # إطار المحتوى
        content_frame = tk.Frame(new_report_frame, padx=20, pady=20)
        content_frame.pack(fill=tk.BOTH, expand=True)

        # اختيار المستخدم
        user_frame = tk.LabelFrame(content_frame, text="اختيار المستخدم",
                                  font=("Arial", 12, "bold"))
        user_frame.pack(fill=tk.X, pady=(0, 15))

        user_inner = tk.Frame(user_frame, padx=15, pady=15)
        user_inner.pack(fill=tk.X)

        user_combo = ttk.Combobox(
            user_inner,
            textvariable=self.selected_user,
            font=("Arial", 11),
            state="readonly",
            width=40
        )

        user_names = ["جميع المستخدمين"] + [f"{user['name']} (ID: {user['id']})" for user in self.users]
        user_combo['values'] = user_names
        user_combo.current(0)
        user_combo.pack(fill=tk.X)

        # الفترة الزمنية
        period_frame = tk.LabelFrame(content_frame, text="الفترة الزمنية",
                                    font=("Arial", 12, "bold"))
        period_frame.pack(fill=tk.X, pady=(0, 15))

        period_inner = tk.Frame(period_frame, padx=15, pady=15)
        period_inner.pack(fill=tk.X)

        # تاريخ البداية
        start_frame = tk.Frame(period_inner)
        start_frame.pack(fill=tk.X, pady=5)

        tk.Label(start_frame, text="من تاريخ:", font=("Arial", 11)).pack(side=tk.LEFT)
        tk.Entry(start_frame, textvariable=self.start_date,
                font=("Arial", 11), width=15).pack(side=tk.RIGHT)

        # تاريخ النهاية
        end_frame = tk.Frame(period_inner)
        end_frame.pack(fill=tk.X, pady=5)

        tk.Label(end_frame, text="إلى تاريخ:", font=("Arial", 11)).pack(side=tk.LEFT)
        tk.Entry(end_frame, textvariable=self.end_date,
                font=("Arial", 11), width=15).pack(side=tk.RIGHT)

        # أزرار الفترات السريعة
        quick_periods_frame = tk.Frame(period_inner)
        quick_periods_frame.pack(fill=tk.X, pady=(10, 0))

        periods = [
            ("آخر أسبوع", 7),
            ("آخر شهر", 30),
            ("آخر 3 أشهر", 90),
            ("آخر سنة", 365)
        ]

        for text, days in periods:
            btn = tk.Button(
                quick_periods_frame,
                text=text,
                command=lambda d=days: self.set_quick_period(d),
                font=("Arial", 10),
                bg="#F18F01",
                fg="white",
                width=10
            )
            btn.pack(side=tk.LEFT, padx=2)

        # نوع التقرير
        type_frame = tk.LabelFrame(content_frame, text="نوع التقرير",
                                  font=("Arial", 12, "bold"))
        type_frame.pack(fill=tk.X, pady=(0, 15))

        type_inner = tk.Frame(type_frame, padx=15, pady=15)
        type_inner.pack(fill=tk.X)

        report_types = [
            ("شامل", "comprehensive"),
            ("ضغط الدم فقط", "blood_pressure"),
            ("السكر فقط", "blood_sugar"),
            ("إحصائيات سريعة", "quick_stats")
        ]

        for text, value in report_types:
            tk.Radiobutton(
                type_inner,
                text=text,
                variable=self.report_type,
                value=value,
                font=("Arial", 11)
            ).pack(anchor=tk.W, pady=2)

        # خيارات التقرير
        options_frame = tk.LabelFrame(content_frame, text="خيارات التقرير",
                                     font=("Arial", 12, "bold"))
        options_frame.pack(fill=tk.X, pady=(0, 15))

        options_inner = tk.Frame(options_frame, padx=15, pady=15)
        options_inner.pack(fill=tk.X)

        tk.Checkbutton(
            options_inner,
            text="تضمين الرسوم البيانية",
            variable=self.include_charts,
            font=("Arial", 11)
        ).pack(anchor=tk.W, pady=2)

        tk.Checkbutton(
            options_inner,
            text="تضمين الإحصائيات التفصيلية",
            variable=self.include_statistics,
            font=("Arial", 11)
        ).pack(anchor=tk.W, pady=2)

        tk.Checkbutton(
            options_inner,
            text="تضمين التوصيات الصحية",
            variable=self.include_recommendations,
            font=("Arial", 11)
        ).pack(anchor=tk.W, pady=2)

        # تنسيق التصدير
        export_frame = tk.LabelFrame(content_frame, text="تنسيق التصدير",
                                    font=("Arial", 12, "bold"))
        export_frame.pack(fill=tk.X, pady=(0, 20))

        export_inner = tk.Frame(export_frame, padx=15, pady=15)
        export_inner.pack(fill=tk.X)

        export_formats = [
            ("PDF", "pdf"),
            ("Excel", "excel"),
            ("كلاهما", "both")
        ]

        for text, value in export_formats:
            tk.Radiobutton(
                export_inner,
                text=text,
                variable=self.export_format,
                value=value,
                font=("Arial", 11)
            ).pack(side=tk.LEFT, padx=10)

        # أزرار الإجراءات
        actions_frame = tk.Frame(content_frame)
        actions_frame.pack(fill=tk.X)

        preview_button = tk.Button(
            actions_frame,
            text="معاينة",
            command=self.preview_report,
            font=("Arial", 12),
            bg="#A23B72",
            fg="white",
            width=12
        )
        preview_button.pack(side=tk.LEFT)

        generate_button = tk.Button(
            actions_frame,
            text="إنشاء التقرير",
            command=self.generate_report,
            font=("Arial", 12),
            bg="#2E86AB",
            fg="white",
            width=15
        )
        generate_button.pack(side=tk.RIGHT)

    def create_saved_reports_tab(self, notebook):
        """إنشاء تبويب التقارير المحفوظة"""
        saved_reports_frame = ttk.Frame(notebook)
        notebook.add(saved_reports_frame, text="التقارير المحفوظة")

        # إطار المحتوى
        content_frame = tk.Frame(saved_reports_frame, padx=20, pady=20)
        content_frame.pack(fill=tk.BOTH, expand=True)

        # شريط الأدوات
        toolbar_frame = tk.Frame(content_frame)
        toolbar_frame.pack(fill=tk.X, pady=(0, 10))

        refresh_button = tk.Button(
            toolbar_frame,
            text="تحديث",
            command=self.refresh_reports_list,
            font=("Arial", 11),
            bg="#F18F01",
            fg="white",
            width=10
        )
        refresh_button.pack(side=tk.LEFT)

        open_folder_button = tk.Button(
            toolbar_frame,
            text="فتح مجلد التقارير",
            command=self.open_reports_folder,
            font=("Arial", 11),
            bg="#A23B72",
            fg="white",
            width=15
        )
        open_folder_button.pack(side=tk.RIGHT)

        # قائمة التقارير
        list_frame = tk.Frame(content_frame)
        list_frame.pack(fill=tk.BOTH, expand=True)

        # إنشاء Treeview للتقارير
        columns = ('الاسم', 'النوع', 'الحجم', 'تاريخ الإنشاء')

        self.reports_tree = ttk.Treeview(
            list_frame,
            columns=columns,
            show='headings',
            height=15
        )

        # تعيين عناوين الأعمدة
        for col in columns:
            self.reports_tree.heading(col, text=col)
            self.reports_tree.column(col, width=150, anchor=tk.CENTER)

        # شريط التمرير
        reports_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL,
                                         command=self.reports_tree.yview)
        self.reports_tree.configure(yscrollcommand=reports_scrollbar.set)

        # تخطيط الجدول وشريط التمرير
        self.reports_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        reports_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # أزرار الإجراءات
        actions_frame = tk.Frame(content_frame)
        actions_frame.pack(fill=tk.X, pady=(10, 0))

        open_button = tk.Button(
            actions_frame,
            text="فتح",
            command=self.open_selected_report,
            font=("Arial", 11),
            bg="#2E86AB",
            fg="white",
            width=10
        )
        open_button.pack(side=tk.LEFT)

        delete_button = tk.Button(
            actions_frame,
            text="حذف",
            command=self.delete_selected_report,
            font=("Arial", 11),
            bg="#C73E1D",
            fg="white",
            width=10
        )
        delete_button.pack(side=tk.LEFT, padx=(10, 0))

        # ربط النقر المزدوج
        self.reports_tree.bind('<Double-1>', lambda e: self.open_selected_report())

    def set_quick_period(self, days):
        """تعيين فترة سريعة"""
        end_date = date.today()
        start_date = end_date - timedelta(days=days)

        self.start_date.set(start_date.strftime("%Y-%m-%d"))
        self.end_date.set(end_date.strftime("%Y-%m-%d"))

    def get_selected_user_id(self):
        """الحصول على معرف المستخدم المختار"""
        selected = self.selected_user.get()
        if selected == "جميع المستخدمين":
            return None

        try:
            # استخراج معرف المستخدم من النص
            user_id = int(selected.split("ID: ")[1].split(")")[0])
            return user_id
        except:
            return None

    def preview_report(self):
        """معاينة التقرير"""
        try:
            user_id = self.get_selected_user_id()
            start_date_obj = datetime.strptime(self.start_date.get(), "%Y-%m-%d").date()
            end_date_obj = datetime.strptime(self.end_date.get(), "%Y-%m-%d").date()

            # الحصول على البيانات
            measurements = self.db_manager.get_measurements(
                user_id=user_id,
                start_date=start_date_obj,
                end_date=end_date_obj
            )

            if not measurements:
                messagebox.showwarning("تحذير", "لا توجد بيانات في الفترة المحددة")
                return

            # إنشاء نافذة المعاينة
            preview_window = tk.Toplevel(self.window)
            preview_window.title("معاينة التقرير")
            preview_window.geometry("600x400")

            # محتوى المعاينة
            text_widget = tk.Text(preview_window, wrap=tk.WORD, font=("Arial", 11))
            text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # إضافة محتوى المعاينة
            preview_content = f"""
معاينة التقرير
================

المستخدم: {self.selected_user.get()}
الفترة: من {self.start_date.get()} إلى {self.end_date.get()}
نوع التقرير: {self.report_type.get()}

إحصائيات سريعة:
- عدد القياسات: {len(measurements)}
- أول قياس: {measurements[-1]['measurement_date'] if measurements else 'لا يوجد'}
- آخر قياس: {measurements[0]['measurement_date'] if measurements else 'لا يوجد'}

سيتم تضمين:
- الرسوم البيانية: {'نعم' if self.include_charts.get() else 'لا'}
- الإحصائيات التفصيلية: {'نعم' if self.include_statistics.get() else 'لا'}
- التوصيات الصحية: {'نعم' if self.include_recommendations.get() else 'لا'}

تنسيق التصدير: {self.export_format.get()}
"""

            text_widget.insert(tk.END, preview_content)
            text_widget.configure(state=tk.DISABLED)

        except Exception as e:
            logger.error(f"خطأ في معاينة التقرير: {e}")
            messagebox.showerror("خطأ", f"فشل في معاينة التقرير:\n{e}")

    def generate_report(self):
        """إنشاء التقرير"""
        try:
            # التحقق من صحة البيانات
            if not self.validate_report_data():
                return

            user_id = self.get_selected_user_id()
            start_date_obj = datetime.strptime(self.start_date.get(), "%Y-%m-%d").date()
            end_date_obj = datetime.strptime(self.end_date.get(), "%Y-%m-%d").date()

            # الحصول على البيانات
            measurements = self.db_manager.get_measurements(
                user_id=user_id,
                start_date=start_date_obj,
                end_date=end_date_obj
            )

            if not measurements:
                messagebox.showwarning("تحذير", "لا توجد بيانات في الفترة المحددة")
                return

            # إنشاء اسم الملف
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            user_name = "جميع_المستخدمين" if not user_id else f"مستخدم_{user_id}"
            base_filename = f"تقرير_{user_name}_{timestamp}"

            generated_files = []

            # إنشاء التقرير حسب التنسيق المختار
            if self.export_format.get() in ["pdf", "both"]:
                pdf_file = self.report_exporter.export_measurements_to_pdf(
                    user_id=user_id,
                    start_date=start_date_obj,
                    end_date=end_date_obj,
                    filename=f"{base_filename}.pdf"
                )
                generated_files.append(pdf_file)

            if self.export_format.get() in ["excel", "both"]:
                excel_file = self.report_exporter.export_measurements_to_excel(
                    user_id=user_id,
                    start_date=start_date_obj,
                    end_date=end_date_obj,
                    filename=f"{base_filename}.xlsx"
                )
                generated_files.append(excel_file)

            # إنشاء تقرير صحي إذا كان مطلوباً
            if self.include_recommendations.get() and user_id:
                health_report = self.data_analyzer.generate_health_report(
                    user_id=user_id,
                    days=(end_date_obj - start_date_obj).days
                )

                if health_report and 'error' not in health_report:
                    health_file = self.report_exporter.export_health_report_to_pdf(
                        user_id=user_id,
                        health_report=health_report,
                        filename=f"تقرير_صحي_{user_name}_{timestamp}.pdf"
                    )
                    generated_files.append(health_file)

            # تحديث قائمة التقارير
            self.refresh_reports_list()

            # إظهار رسالة النجاح
            files_text = "\n".join([os.path.basename(f) for f in generated_files])
            messagebox.showinfo("نجح", f"تم إنشاء التقارير بنجاح:\n\n{files_text}")

        except Exception as e:
            logger.error(f"خطأ في إنشاء التقرير: {e}")
            messagebox.showerror("خطأ", f"فشل في إنشاء التقرير:\n{e}")

    def validate_report_data(self):
        """التحقق من صحة بيانات التقرير"""
        try:
            # التحقق من التواريخ
            start_date_obj = datetime.strptime(self.start_date.get(), "%Y-%m-%d").date()
            end_date_obj = datetime.strptime(self.end_date.get(), "%Y-%m-%d").date()

            if start_date_obj > end_date_obj:
                messagebox.showerror("خطأ", "تاريخ البداية يجب أن يكون قبل تاريخ النهاية")
                return False

            if end_date_obj > date.today():
                messagebox.showerror("خطأ", "تاريخ النهاية لا يمكن أن يكون في المستقبل")
                return False

            return True

        except ValueError:
            messagebox.showerror("خطأ", "تنسيق التاريخ غير صحيح (YYYY-MM-DD)")
            return False
        except Exception as e:
            logger.error(f"خطأ في التحقق من البيانات: {e}")
            return False

    def refresh_reports_list(self):
        """تحديث قائمة التقارير"""
        try:
            # مسح القائمة الحالية
            for item in self.reports_tree.get_children():
                self.reports_tree.delete(item)

            # الحصول على التقارير المتاحة
            reports = self.report_exporter.get_available_reports()

            # إضافة التقارير إلى القائمة
            for report in reports:
                # تحويل الحجم إلى تنسيق قابل للقراءة
                size_mb = report['size'] / (1024 * 1024)
                size_text = f"{size_mb:.2f} MB" if size_mb >= 1 else f"{report['size']/1024:.1f} KB"

                # تنسيق التاريخ
                date_text = report['created_at'].strftime("%Y-%m-%d %H:%M")

                self.reports_tree.insert('', tk.END, values=(
                    report['filename'],
                    report['type'],
                    size_text,
                    date_text
                ))

        except Exception as e:
            logger.error(f"خطأ في تحديث قائمة التقارير: {e}")

    def open_reports_folder(self):
        """فتح مجلد التقارير"""
        try:
            reports_dir = "reports"
            if os.path.exists(reports_dir):
                if os.name == 'nt':  # Windows
                    os.startfile(reports_dir)
                elif os.name == 'posix':  # Linux/Mac
                    os.system(f'open "{reports_dir}"' if os.uname().sysname == 'Darwin' else f'xdg-open "{reports_dir}"')
            else:
                messagebox.showwarning("تحذير", "مجلد التقارير غير موجود")

        except Exception as e:
            logger.error(f"خطأ في فتح مجلد التقارير: {e}")
            messagebox.showerror("خطأ", f"فشل في فتح مجلد التقارير:\n{e}")

    def open_selected_report(self):
        """فتح التقرير المختار"""
        try:
            selection = self.reports_tree.selection()
            if not selection:
                messagebox.showwarning("تحذير", "يجب اختيار تقرير أولاً")
                return

            item = self.reports_tree.item(selection[0])
            filename = item['values'][0]
            filepath = os.path.join("reports", filename)

            if os.path.exists(filepath):
                if os.name == 'nt':  # Windows
                    os.startfile(filepath)
                elif os.name == 'posix':  # Linux/Mac
                    os.system(f'open "{filepath}"' if os.uname().sysname == 'Darwin' else f'xdg-open "{filepath}"')
            else:
                messagebox.showerror("خطأ", "الملف غير موجود")

        except Exception as e:
            logger.error(f"خطأ في فتح التقرير: {e}")
            messagebox.showerror("خطأ", f"فشل في فتح التقرير:\n{e}")

    def delete_selected_report(self):
        """حذف التقرير المختار"""
        try:
            selection = self.reports_tree.selection()
            if not selection:
                messagebox.showwarning("تحذير", "يجب اختيار تقرير أولاً")
                return

            item = self.reports_tree.item(selection[0])
            filename = item['values'][0]
            filepath = os.path.join("reports", filename)

            # تأكيد الحذف
            result = messagebox.askyesno(
                "تأكيد الحذف",
                f"هل أنت متأكد من حذف التقرير:\n{filename}؟"
            )

            if result and os.path.exists(filepath):
                os.remove(filepath)
                self.refresh_reports_list()
                messagebox.showinfo("تم", "تم حذف التقرير بنجاح")

        except Exception as e:
            logger.error(f"خطأ في حذف التقرير: {e}")
            messagebox.showerror("خطأ", f"فشل في حذف التقرير:\n{e}")

    def show(self):
        """عرض النافذة"""
        self.window.mainloop()

class ChartsWindow:
    """نافذة الرسوم البيانية التفاعلية"""

    def __init__(self, parent, db_manager, data_analyzer):
        """
        إنشاء نافذة الرسوم البيانية

        Args:
            parent: النافذة الأب
            db_manager: مدير قاعدة البيانات
            data_analyzer: محلل البيانات
        """
        self.parent = parent
        self.db_manager = db_manager
        self.data_analyzer = data_analyzer

        # إنشاء النافذة
        self.window = tk.Toplevel(parent)
        self.window.title("الرسوم البيانية")
        self.window.geometry("1000x700")
        self.window.resizable(True, True)
        self.window.transient(parent)
        self.window.grab_set()

        # وضع النافذة في المنتصف
        self.center_window()

        # متغيرات النموذج
        self.setup_variables()

        # تحميل المستخدمين
        self.users = self.db_manager.get_users()

        # متغيرات الرسم
        self.current_figure = None
        self.canvas = None
        self.toolbar = None

        self.create_widgets()

        # رسم أولي
        self.update_chart()

    def center_window(self):
        """وضع النافذة في منتصف الشاشة"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (1000 // 2)
        y = (self.window.winfo_screenheight() // 2) - (700 // 2)
        self.window.geometry(f"1000x700+{x}+{y}")

    def setup_variables(self):
        """إعداد متغيرات النموذج"""
        self.selected_user = tk.StringVar()
        self.chart_type = tk.StringVar(value="blood_pressure")
        self.time_period = tk.StringVar(value="30")
        self.start_date = tk.StringVar(value=(date.today() - timedelta(days=30)).strftime("%Y-%m-%d"))
        self.end_date = tk.StringVar(value=date.today().strftime("%Y-%m-%d"))
        self.show_trend_line = tk.BooleanVar(value=True)
        self.show_normal_ranges = tk.BooleanVar(value=True)
        self.chart_style = tk.StringVar(value="default")

    def create_widgets(self):
        """إنشاء عناصر النافذة"""
        # إطار رئيسي
        main_frame = tk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # إطار التحكم (يسار)
        control_frame = tk.Frame(main_frame, width=250, bg="#F5F5F5")
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        control_frame.pack_propagate(False)

        # إطار الرسم (يمين)
        chart_frame = tk.Frame(main_frame, bg="white")
        chart_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # إنشاء عناصر التحكم
        self.create_control_panel(control_frame)

        # إنشاء منطقة الرسم
        self.create_chart_area(chart_frame)

    def create_control_panel(self, parent):
        """إنشاء لوحة التحكم"""
        # عنوان
        title_label = tk.Label(
            parent,
            text="خيارات الرسم",
            font=("Arial", 14, "bold"),
            bg="#F5F5F5",
            fg="#2E86AB"
        )
        title_label.pack(pady=(10, 20))

        # اختيار المستخدم
        user_frame = tk.LabelFrame(parent, text="المستخدم", font=("Arial", 11, "bold"), bg="#F5F5F5")
        user_frame.pack(fill=tk.X, padx=10, pady=(0, 15))

        user_inner = tk.Frame(user_frame, bg="#F5F5F5")
        user_inner.pack(fill=tk.X, padx=10, pady=10)

        user_combo = ttk.Combobox(
            user_inner,
            textvariable=self.selected_user,
            font=("Arial", 10),
            state="readonly",
            width=25
        )

        user_names = [f"{user['name']} (ID: {user['id']})" for user in self.users]
        user_combo['values'] = user_names
        if user_names:
            user_combo.current(0)
        user_combo.pack(fill=tk.X)
        user_combo.bind('<<ComboboxSelected>>', lambda e: self.update_chart())

        # نوع الرسم
        chart_type_frame = tk.LabelFrame(parent, text="نوع الرسم", font=("Arial", 11, "bold"), bg="#F5F5F5")
        chart_type_frame.pack(fill=tk.X, padx=10, pady=(0, 15))

        chart_inner = tk.Frame(chart_type_frame, bg="#F5F5F5")
        chart_inner.pack(fill=tk.X, padx=10, pady=10)

        chart_types = [
            ("ضغط الدم", "blood_pressure"),
            ("السكر", "blood_sugar"),
            ("مدمج", "combined"),
            ("إحصائيات", "statistics")
        ]

        for text, value in chart_types:
            tk.Radiobutton(
                chart_inner,
                text=text,
                variable=self.chart_type,
                value=value,
                font=("Arial", 10),
                bg="#F5F5F5",
                command=self.update_chart
            ).pack(anchor=tk.W, pady=2)

        # الفترة الزمنية
        period_frame = tk.LabelFrame(parent, text="الفترة الزمنية", font=("Arial", 11, "bold"), bg="#F5F5F5")
        period_frame.pack(fill=tk.X, padx=10, pady=(0, 15))

        period_inner = tk.Frame(period_frame, bg="#F5F5F5")
        period_inner.pack(fill=tk.X, padx=10, pady=10)

        # فترات سريعة
        periods = [
            ("آخر أسبوع", "7"),
            ("آخر شهر", "30"),
            ("آخر 3 أشهر", "90"),
            ("مخصص", "custom")
        ]

        for text, value in periods:
            tk.Radiobutton(
                period_inner,
                text=text,
                variable=self.time_period,
                value=value,
                font=("Arial", 10),
                bg="#F5F5F5",
                command=self.on_period_change
            ).pack(anchor=tk.W, pady=2)

        # تواريخ مخصصة
        self.custom_dates_frame = tk.Frame(period_inner, bg="#F5F5F5")
        self.custom_dates_frame.pack(fill=tk.X, pady=(10, 0))

        tk.Label(self.custom_dates_frame, text="من:", font=("Arial", 9), bg="#F5F5F5").pack(anchor=tk.W)
        tk.Entry(self.custom_dates_frame, textvariable=self.start_date,
                font=("Arial", 9), width=12).pack(fill=tk.X, pady=(2, 5))

        tk.Label(self.custom_dates_frame, text="إلى:", font=("Arial", 9), bg="#F5F5F5").pack(anchor=tk.W)
        tk.Entry(self.custom_dates_frame, textvariable=self.end_date,
                font=("Arial", 9), width=12).pack(fill=tk.X, pady=(2, 5))

        update_custom_btn = tk.Button(
            self.custom_dates_frame,
            text="تحديث",
            command=self.update_chart,
            font=("Arial", 9),
            bg="#2E86AB",
            fg="white",
            width=10
        )
        update_custom_btn.pack(pady=(5, 0))

        # إخفاء التواريخ المخصصة في البداية
        self.custom_dates_frame.pack_forget()

        # خيارات الرسم
        options_frame = tk.LabelFrame(parent, text="خيارات الرسم", font=("Arial", 11, "bold"), bg="#F5F5F5")
        options_frame.pack(fill=tk.X, padx=10, pady=(0, 15))

        options_inner = tk.Frame(options_frame, bg="#F5F5F5")
        options_inner.pack(fill=tk.X, padx=10, pady=10)

        tk.Checkbutton(
            options_inner,
            text="خط الاتجاه",
            variable=self.show_trend_line,
            font=("Arial", 10),
            bg="#F5F5F5",
            command=self.update_chart
        ).pack(anchor=tk.W, pady=2)

        tk.Checkbutton(
            options_inner,
            text="النطاقات الطبيعية",
            variable=self.show_normal_ranges,
            font=("Arial", 10),
            bg="#F5F5F5",
            command=self.update_chart
        ).pack(anchor=tk.W, pady=2)

        # نمط الرسم
        style_frame = tk.LabelFrame(parent, text="نمط الرسم", font=("Arial", 11, "bold"), bg="#F5F5F5")
        style_frame.pack(fill=tk.X, padx=10, pady=(0, 15))

        style_inner = tk.Frame(style_frame, bg="#F5F5F5")
        style_inner.pack(fill=tk.X, padx=10, pady=10)

        style_combo = ttk.Combobox(
            style_inner,
            textvariable=self.chart_style,
            values=["default", "seaborn", "ggplot", "classic"],
            font=("Arial", 10),
            state="readonly",
            width=20
        )
        style_combo.pack(fill=tk.X)
        style_combo.bind('<<ComboboxSelected>>', lambda e: self.update_chart())

        # أزرار الإجراءات
        actions_frame = tk.Frame(parent, bg="#F5F5F5")
        actions_frame.pack(fill=tk.X, padx=10, pady=(20, 10))

        refresh_btn = tk.Button(
            actions_frame,
            text="تحديث",
            command=self.update_chart,
            font=("Arial", 11),
            bg="#F18F01",
            fg="white",
            width=12
        )
        refresh_btn.pack(fill=tk.X, pady=(0, 5))

        save_btn = tk.Button(
            actions_frame,
            text="حفظ الرسم",
            command=self.save_chart,
            font=("Arial", 11),
            bg="#A23B72",
            fg="white",
            width=12
        )
        save_btn.pack(fill=tk.X)

    def create_chart_area(self, parent):
        """إنشاء منطقة الرسم"""
        # إطار الرسم
        self.chart_container = tk.Frame(parent, bg="white")
        self.chart_container.pack(fill=tk.BOTH, expand=True)

    def on_period_change(self):
        """عند تغيير الفترة الزمنية"""
        if self.time_period.get() == "custom":
            self.custom_dates_frame.pack(fill=tk.X, pady=(10, 0))
        else:
            self.custom_dates_frame.pack_forget()
            self.update_chart()

    def get_selected_user_id(self):
        """الحصول على معرف المستخدم المختار"""
        selected = self.selected_user.get()
        if not selected:
            return None

        try:
            user_id = int(selected.split("ID: ")[1].split(")")[0])
            return user_id
        except:
            return None

    def get_date_range(self):
        """الحصول على نطاق التواريخ"""
        try:
            if self.time_period.get() == "custom":
                start_date_obj = datetime.strptime(self.start_date.get(), "%Y-%m-%d").date()
                end_date_obj = datetime.strptime(self.end_date.get(), "%Y-%m-%d").date()
            else:
                days = int(self.time_period.get())
                end_date_obj = date.today()
                start_date_obj = end_date_obj - timedelta(days=days)

            return start_date_obj, end_date_obj

        except ValueError:
            # في حالة خطأ في التاريخ، استخدم آخر 30 يوم
            end_date_obj = date.today()
            start_date_obj = end_date_obj - timedelta(days=30)
            return start_date_obj, end_date_obj

    def update_chart(self):
        """تحديث الرسم البياني"""
        try:
            user_id = self.get_selected_user_id()
            if not user_id:
                self.show_no_data_message("يجب اختيار مستخدم أولاً")
                return

            start_date_obj, end_date_obj = self.get_date_range()

            # تطبيق نمط الرسم
            plt.style.use(self.chart_style.get())

            # إنشاء الرسم حسب النوع المختار
            if self.chart_type.get() == "blood_pressure":
                figure = self.data_analyzer.create_blood_pressure_chart(user_id, (end_date_obj - start_date_obj).days)
            elif self.chart_type.get() == "blood_sugar":
                figure = self.data_analyzer.create_blood_sugar_chart(user_id, (end_date_obj - start_date_obj).days)
            elif self.chart_type.get() == "combined":
                figure = self.data_analyzer.create_combined_chart(user_id, (end_date_obj - start_date_obj).days)
            elif self.chart_type.get() == "statistics":
                figure = self.create_statistics_chart(user_id, start_date_obj, end_date_obj)
            else:
                self.show_no_data_message("نوع رسم غير مدعوم")
                return

            if figure:
                self.display_chart(figure)
            else:
                self.show_no_data_message("لا توجد بيانات للعرض")

        except Exception as e:
            logger.error(f"خطأ في تحديث الرسم: {e}")
            self.show_no_data_message(f"خطأ في إنشاء الرسم: {e}")

    def create_statistics_chart(self, user_id, start_date_obj, end_date_obj):
        """إنشاء رسم الإحصائيات"""
        try:
            # الحصول على الإحصائيات
            days = (end_date_obj - start_date_obj).days
            stats = self.data_analyzer.calculate_statistics(user_id, days)

            if not stats:
                return None

            # إنشاء رسم الإحصائيات
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 8))
            fig.suptitle(f'إحصائيات القياسات - آخر {days} يوم', fontsize=16)

            # رسم توزيع ضغط الدم
            if 'blood_pressure' in stats:
                bp_stats = stats['blood_pressure']
                categories = bp_stats.get('categories', {})

                if categories:
                    labels = ['طبيعي', 'مرتفع قليلاً', 'مرحلة 1', 'مرحلة 2', 'أزمة']
                    values = [
                        categories.get('normal', 0),
                        categories.get('elevated', 0),
                        categories.get('stage1', 0),
                        categories.get('stage2', 0),
                        categories.get('crisis', 0)
                    ]
                    colors = ['green', 'yellow', 'orange', 'red', 'darkred']

                    ax1.pie(values, labels=labels, colors=colors, autopct='%1.1f%%')
                    ax1.set_title('توزيع قراءات ضغط الدم')

            # رسم توزيع السكر
            if 'blood_sugar' in stats:
                sugar_stats = stats['blood_sugar']
                categories = sugar_stats.get('categories', {})

                if categories:
                    labels = ['منخفض', 'طبيعي', 'مرتفع', 'مرتفع جداً']
                    values = [
                        categories.get('low', 0),
                        categories.get('normal', 0),
                        categories.get('high', 0),
                        categories.get('very_high', 0)
                    ]
                    colors = ['blue', 'green', 'orange', 'red']

                    ax2.pie(values, labels=labels, colors=colors, autopct='%1.1f%%')
                    ax2.set_title('توزيع قراءات السكر')

            # رسم متوسطات ضغط الدم
            if 'blood_pressure' in stats:
                bp_stats = stats['blood_pressure']
                categories = ['المتوسط', 'الأدنى', 'الأعلى']
                systolic_values = [
                    bp_stats['systolic']['mean'],
                    bp_stats['systolic']['min'],
                    bp_stats['systolic']['max']
                ]
                diastolic_values = [
                    bp_stats['diastolic']['mean'],
                    bp_stats['diastolic']['min'],
                    bp_stats['diastolic']['max']
                ]

                x = range(len(categories))
                width = 0.35

                ax3.bar([i - width/2 for i in x], systolic_values, width, label='انقباضي', color='red', alpha=0.7)
                ax3.bar([i + width/2 for i in x], diastolic_values, width, label='انبساطي', color='blue', alpha=0.7)

                ax3.set_xlabel('الإحصائية')
                ax3.set_ylabel('ضغط الدم (mmHg)')
                ax3.set_title('إحصائيات ضغط الدم')
                ax3.set_xticks(x)
                ax3.set_xticklabels(categories)
                ax3.legend()

            # رسم متوسطات السكر
            if 'blood_sugar' in stats:
                sugar_stats = stats['blood_sugar']

                data_to_plot = []
                labels_to_plot = []

                if 'overall' in sugar_stats:
                    overall = sugar_stats['overall']
                    data_to_plot.extend([overall['mean'], overall['min'], overall['max']])
                    labels_to_plot.extend(['المتوسط العام', 'الأدنى', 'الأعلى'])

                if data_to_plot:
                    ax4.bar(labels_to_plot, data_to_plot, color=['green', 'blue', 'red'], alpha=0.7)
                    ax4.set_ylabel('مستوى السكر (mg/dL)')
                    ax4.set_title('إحصائيات السكر')
                    ax4.tick_params(axis='x', rotation=45)

            plt.tight_layout()
            return fig

        except Exception as e:
            logger.error(f"خطأ في إنشاء رسم الإحصائيات: {e}")
            return None

    def display_chart(self, figure):
        """عرض الرسم البياني"""
        try:
            # إزالة الرسم السابق
            if self.canvas:
                self.canvas.get_tk_widget().destroy()
            if self.toolbar:
                self.toolbar.destroy()

            # إنشاء canvas جديد
            self.canvas = FigureCanvasTkAgg(figure, self.chart_container)
            self.canvas.draw()
            self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

            # إضافة شريط الأدوات
            self.toolbar = NavigationToolbar2Tk(self.canvas, self.chart_container)
            self.toolbar.update()

            # حفظ المرجع للرسم الحالي
            self.current_figure = figure

        except Exception as e:
            logger.error(f"خطأ في عرض الرسم: {e}")
            self.show_no_data_message(f"خطأ في عرض الرسم: {e}")

    def show_no_data_message(self, message):
        """عرض رسالة عدم وجود بيانات"""
        try:
            # إزالة الرسم السابق
            if self.canvas:
                self.canvas.get_tk_widget().destroy()
                self.canvas = None
            if self.toolbar:
                self.toolbar.destroy()
                self.toolbar = None

            # إنشاء رسالة
            message_label = tk.Label(
                self.chart_container,
                text=message,
                font=("Arial", 14),
                fg="gray",
                bg="white"
            )
            message_label.pack(expand=True)

        except Exception as e:
            logger.error(f"خطأ في عرض رسالة عدم وجود البيانات: {e}")

    def save_chart(self):
        """حفظ الرسم البياني"""
        try:
            if not self.current_figure:
                messagebox.showwarning("تحذير", "لا يوجد رسم لحفظه")
                return

            # اختيار مكان الحفظ
            filename = filedialog.asksaveasfilename(
                title="حفظ الرسم البياني",
                defaultextension=".png",
                filetypes=[
                    ("PNG files", "*.png"),
                    ("PDF files", "*.pdf"),
                    ("SVG files", "*.svg"),
                    ("All files", "*.*")
                ]
            )

            if filename:
                self.current_figure.savefig(filename, dpi=300, bbox_inches='tight')
                messagebox.showinfo("نجح", f"تم حفظ الرسم بنجاح:\n{filename}")

        except Exception as e:
            logger.error(f"خطأ في حفظ الرسم: {e}")
            messagebox.showerror("خطأ", f"فشل في حفظ الرسم:\n{e}")

    def show(self):
        """عرض النافذة"""
        self.window.mainloop()