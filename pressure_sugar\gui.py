#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة المستخدم الرئيسية
Main User Interface Module

تحتوي على جميع واجهات المستخدم للتطبيق
باستخدام tkinter مع دعم اللغة العربية

المطور: مساعد الذكي الاصطناعي
التاريخ: 2025-06-21
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime, date
import logging
from typing import Optional, Dict, List
import os

# استيراد الوحدات المحلية
from database import DatabaseManager
from analysis import DataAnalyzer
from export import ReportExporter
from dialogs import NewUserDialog, NewMeasurementDialog

logger = logging.getLogger(__name__)

class MainApplication:
    """الفئة الرئيسية لواجهة المستخدم"""
    
    def __init__(self, root: tk.Tk, db_manager: DatabaseManager,
                 data_analyzer: DataAnalyzer, report_exporter: ReportExporter,
                 notification_manager=None):
        """
        تهيئة واجهة المستخدم الرئيسية
        
        Args:
            root: النافذة الرئيسية
            db_manager: مدير قاعدة البيانات
            data_analyzer: محلل البيانات
            report_exporter: مصدر التقارير
        """
        self.root = root
        self.db_manager = db_manager
        self.data_analyzer = data_analyzer
        self.report_exporter = report_exporter
        self.notification_manager = notification_manager
        
        # المتغيرات
        self.current_user_id = None
        self.users = []
        
        # إعداد النافذة الرئيسية
        self.setup_main_window()
        
        # إنشاء واجهة المستخدم
        self.create_widgets()
        
        # تحميل البيانات الأولية
        self.load_initial_data()
    
    def setup_main_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title("برنامج ضغط وسكر - حفظ القياسات وتحليلها")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)
        
        # تعيين الأيقونة (إذا كانت متوفرة)
        try:
            self.root.iconbitmap("assets/icon.ico")
        except:
            pass
        
        # إعداد الخط العربي
        self.setup_arabic_font()
        
        # إعداد الألوان والثيم
        self.setup_theme()
    
    def setup_arabic_font(self):
        """إعداد الخط العربي"""
        try:
            # تجربة خطوط عربية مختلفة
            arabic_fonts = [
                ("Arial Unicode MS", 12),
                ("Tahoma", 12),
                ("Segoe UI", 12),
                ("DejaVu Sans", 12)
            ]
            
            self.arabic_font = None
            for font_name, size in arabic_fonts:
                try:
                    test_font = (font_name, size)
                    # اختبار الخط
                    test_label = tk.Label(self.root, text="اختبار", font=test_font)
                    test_label.destroy()
                    self.arabic_font = test_font
                    break
                except:
                    continue
            
            if not self.arabic_font:
                self.arabic_font = ("Arial", 12)
                
            # خطوط مختلفة للعناوين والنصوص
            self.title_font = (self.arabic_font[0], 16, "bold")
            self.header_font = (self.arabic_font[0], 14, "bold")
            self.normal_font = self.arabic_font
            self.small_font = (self.arabic_font[0], 10)
            
        except Exception as e:
            logger.warning(f"تحذير في إعداد الخط العربي: {e}")
            self.arabic_font = ("Arial", 12)
            self.title_font = ("Arial", 16, "bold")
            self.header_font = ("Arial", 14, "bold")
            self.normal_font = ("Arial", 12)
            self.small_font = ("Arial", 10)
    
    def setup_theme(self):
        """إعداد الألوان والثيم"""
        self.colors = {
            'primary': '#2E86AB',      # أزرق رئيسي
            'secondary': '#A23B72',    # وردي ثانوي
            'success': '#F18F01',      # برتقالي للنجاح
            'warning': '#C73E1D',      # أحمر للتحذير
            'background': '#F5F5F5',   # خلفية رمادية فاتحة
            'white': '#FFFFFF',        # أبيض
            'text': '#333333',         # نص رمادي داكن
            'light_gray': '#E0E0E0'    # رمادي فاتح
        }
        
        # تطبيق لون الخلفية
        self.root.configure(bg=self.colors['background'])
    
    def create_widgets(self):
        """إنشاء عناصر واجهة المستخدم"""
        # إنشاء الإطار الرئيسي
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # إنشاء شريط العنوان
        self.create_title_bar()
        
        # إنشاء شريط الأدوات
        self.create_toolbar()
        
        # إنشاء المحتوى الرئيسي
        self.create_main_content()
        
        # إنشاء شريط الحالة
        self.create_status_bar()
    
    def create_title_bar(self):
        """إنشاء شريط العنوان"""
        title_frame = tk.Frame(self.main_frame, bg=self.colors['primary'], height=60)
        title_frame.pack(fill=tk.X, pady=(0, 10))
        title_frame.pack_propagate(False)
        
        # عنوان التطبيق
        title_label = tk.Label(
            title_frame,
            text="برنامج ضغط وسكر - حفظ القياسات وتحليلها",
            font=self.title_font,
            bg=self.colors['primary'],
            fg=self.colors['white']
        )
        title_label.pack(expand=True)
        
        # معلومات المستخدم الحالي
        self.user_info_label = tk.Label(
            title_frame,
            text="لم يتم اختيار مستخدم",
            font=self.small_font,
            bg=self.colors['primary'],
            fg=self.colors['white']
        )
        self.user_info_label.pack(side=tk.RIGHT, padx=10)
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar_frame = tk.Frame(self.main_frame, bg=self.colors['background'])
        toolbar_frame.pack(fill=tk.X, pady=(0, 10))
        
        # أزرار شريط الأدوات
        buttons = [
            ("مستخدم جديد", self.show_new_user_dialog, self.colors['primary']),
            ("قياس جديد", self.show_new_measurement_dialog, self.colors['success']),
            ("عرض القياسات", self.show_measurements_view, self.colors['secondary']),
            ("الرسوم البيانية", self.show_charts_view, self.colors['primary']),
            ("التقارير", self.show_reports_view, self.colors['success']),
            ("الإعدادات", self.show_settings_dialog, self.colors['text'])
        ]
        
        for text, command, color in buttons:
            btn = tk.Button(
                toolbar_frame,
                text=text,
                font=self.normal_font,
                bg=color,
                fg=self.colors['white'],
                relief=tk.FLAT,
                padx=15,
                pady=8,
                command=command,
                cursor="hand2"
            )
            btn.pack(side=tk.LEFT, padx=5)
            
            # تأثير hover
            self.add_hover_effect(btn, color)
    
    def add_hover_effect(self, button, original_color):
        """إضافة تأثير hover للأزرار"""
        def on_enter(e):
            button.configure(bg=self.lighten_color(original_color))
        
        def on_leave(e):
            button.configure(bg=original_color)
        
        button.bind("<Enter>", on_enter)
        button.bind("<Leave>", on_leave)
    
    def lighten_color(self, color):
        """تفتيح لون للتأثير"""
        # تحويل بسيط للون - يمكن تحسينه
        if color == self.colors['primary']:
            return '#4A9BC7'
        elif color == self.colors['success']:
            return '#F4A633'
        elif color == self.colors['secondary']:
            return '#B85A8A'
        elif color == self.colors['warning']:
            return '#D85A47'
        else:
            return '#555555'
    
    def create_main_content(self):
        """إنشاء المحتوى الرئيسي"""
        # إطار المحتوى الرئيسي
        self.content_frame = tk.Frame(self.main_frame, bg=self.colors['white'])
        self.content_frame.pack(fill=tk.BOTH, expand=True)
        
        # إنشاء notebook للتبويبات
        self.notebook = ttk.Notebook(self.content_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # تبويب الرئيسية
        self.create_home_tab()
        
        # تبويب القياسات
        self.create_measurements_tab()
        
        # تبويب الإحصائيات
        self.create_statistics_tab()
    
    def create_home_tab(self):
        """إنشاء تبويب الصفحة الرئيسية"""
        home_frame = ttk.Frame(self.notebook)
        self.notebook.add(home_frame, text="الرئيسية")
        
        # رسالة ترحيب
        welcome_label = tk.Label(
            home_frame,
            text="مرحباً بك في برنامج ضغط وسكر",
            font=self.header_font,
            bg=self.colors['white'],
            fg=self.colors['text']
        )
        welcome_label.pack(pady=20)
        
        # معلومات سريعة
        info_frame = tk.Frame(home_frame, bg=self.colors['white'])
        info_frame.pack(fill=tk.BOTH, expand=True, padx=20)
        
        # إحصائيات سريعة
        self.create_quick_stats(info_frame)
        
        # التنبيهات الأخيرة
        self.create_recent_alerts(info_frame)
    
    def create_quick_stats(self, parent):
        """إنشاء الإحصائيات السريعة"""
        stats_frame = tk.LabelFrame(
            parent,
            text="إحصائيات سريعة",
            font=self.header_font,
            bg=self.colors['white'],
            fg=self.colors['text']
        )
        stats_frame.pack(fill=tk.X, pady=10)
        
        # إطار للإحصائيات
        stats_content = tk.Frame(stats_frame, bg=self.colors['white'])
        stats_content.pack(fill=tk.X, padx=10, pady=10)
        
        # عدد القياسات اليوم
        self.today_measurements_label = tk.Label(
            stats_content,
            text="قياسات اليوم: 0",
            font=self.normal_font,
            bg=self.colors['white'],
            fg=self.colors['text']
        )
        self.today_measurements_label.pack(anchor=tk.W)
        
        # آخر قياس ضغط
        self.last_bp_label = tk.Label(
            stats_content,
            text="آخر قياس ضغط: --/--",
            font=self.normal_font,
            bg=self.colors['white'],
            fg=self.colors['text']
        )
        self.last_bp_label.pack(anchor=tk.W)
        
        # آخر قياس سكر
        self.last_sugar_label = tk.Label(
            stats_content,
            text="آخر قياس سكر: -- mg/dL",
            font=self.normal_font,
            bg=self.colors['white'],
            fg=self.colors['text']
        )
        self.last_sugar_label.pack(anchor=tk.W)
    
    def create_recent_alerts(self, parent):
        """إنشاء التنبيهات الأخيرة"""
        alerts_frame = tk.LabelFrame(
            parent,
            text="التنبيهات الأخيرة",
            font=self.header_font,
            bg=self.colors['white'],
            fg=self.colors['text']
        )
        alerts_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # قائمة التنبيهات
        self.alerts_listbox = tk.Listbox(
            alerts_frame,
            font=self.normal_font,
            bg=self.colors['white'],
            fg=self.colors['text'],
            height=5
        )
        self.alerts_listbox.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    def create_measurements_tab(self):
        """إنشاء تبويب القياسات"""
        measurements_frame = ttk.Frame(self.notebook)
        self.notebook.add(measurements_frame, text="القياسات")
        
        # جدول القياسات
        self.create_measurements_table(measurements_frame)
    
    def create_measurements_table(self, parent):
        """إنشاء جدول القياسات"""
        # إطار الجدول
        table_frame = tk.Frame(parent, bg=self.colors['white'])
        table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # عنوان الجدول
        table_title = tk.Label(
            table_frame,
            text="سجل القياسات",
            font=self.header_font,
            bg=self.colors['white'],
            fg=self.colors['text']
        )
        table_title.pack(pady=(0, 10))
        
        # إنشاء Treeview للجدول
        columns = ('التاريخ', 'الوقت', 'الضغط الانقباضي', 'الضغط الانبساطي', 'السكر', 'النوع', 'ملاحظات')
        
        self.measurements_tree = ttk.Treeview(
            table_frame,
            columns=columns,
            show='headings',
            height=15
        )
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.measurements_tree.heading(col, text=col)
            self.measurements_tree.column(col, width=120, anchor=tk.CENTER)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.measurements_tree.yview)
        self.measurements_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول وشريط التمرير
        self.measurements_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_statistics_tab(self):
        """إنشاء تبويب الإحصائيات"""
        stats_frame = ttk.Frame(self.notebook)
        self.notebook.add(stats_frame, text="الإحصائيات")
        
        # محتوى الإحصائيات
        stats_content = tk.Label(
            stats_frame,
            text="سيتم إضافة الإحصائيات والرسوم البيانية هنا",
            font=self.header_font,
            bg=self.colors['white'],
            fg=self.colors['text']
        )
        stats_content.pack(expand=True)
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_bar = tk.Frame(self.main_frame, bg=self.colors['light_gray'], height=25)
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM)
        self.status_bar.pack_propagate(False)
        
        self.status_label = tk.Label(
            self.status_bar,
            text="جاهز",
            font=self.small_font,
            bg=self.colors['light_gray'],
            fg=self.colors['text']
        )
        self.status_label.pack(side=tk.LEFT, padx=10)
        
        # تاريخ ووقت
        self.datetime_label = tk.Label(
            self.status_bar,
            text="",
            font=self.small_font,
            bg=self.colors['light_gray'],
            fg=self.colors['text']
        )
        self.datetime_label.pack(side=tk.RIGHT, padx=10)
        
        # تحديث الوقت
        self.update_datetime()
    
    def update_datetime(self):
        """تحديث التاريخ والوقت"""
        now = datetime.now()
        datetime_str = now.strftime("%Y-%m-%d %H:%M:%S")
        self.datetime_label.configure(text=datetime_str)
        
        # تحديث كل ثانية
        self.root.after(1000, self.update_datetime)
    
    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        try:
            # تحميل المستخدمين
            self.users = self.db_manager.get_users()
            
            # إذا كان هناك مستخدم واحد فقط، اختره تلقائياً
            if len(self.users) == 1:
                self.current_user_id = self.users[0]['id']
                self.update_user_info()
            
            # تحديث الإحصائيات السريعة
            self.update_quick_stats()
            
            # تحديث التنبيهات
            self.update_recent_alerts()
            
            # تحديث جدول القياسات
            self.update_measurements_table()
            
            self.update_status("تم تحميل البيانات بنجاح")
            
        except Exception as e:
            logger.error(f"خطأ في تحميل البيانات الأولية: {e}")
            self.update_status(f"خطأ في تحميل البيانات: {e}")
    
    def update_status(self, message: str):
        """تحديث رسالة شريط الحالة"""
        self.status_label.configure(text=message)
        logger.info(f"Status: {message}")
    
    def update_user_info(self):
        """تحديث معلومات المستخدم الحالي"""
        if self.current_user_id:
            user = next((u for u in self.users if u['id'] == self.current_user_id), None)
            if user:
                self.user_info_label.configure(text=f"المستخدم: {user['name']}")
            else:
                self.user_info_label.configure(text="مستخدم غير معروف")
        else:
            self.user_info_label.configure(text="لم يتم اختيار مستخدم")
    
    def update_quick_stats(self):
        """تحديث الإحصائيات السريعة"""
        if not self.current_user_id:
            return
        
        try:
            # قياسات اليوم
            today_measurements = self.db_manager.get_measurements(
                user_id=self.current_user_id,
                start_date=date.today(),
                end_date=date.today()
            )
            
            self.today_measurements_label.configure(
                text=f"قياسات اليوم: {len(today_measurements)}"
            )
            
            # آخر القياسات
            recent_measurements = self.db_manager.get_measurements(
                user_id=self.current_user_id,
                limit=1
            )
            
            if recent_measurements:
                last = recent_measurements[0]
                
                # آخر قياس ضغط
                if last['systolic_pressure'] and last['diastolic_pressure']:
                    bp_text = f"آخر قياس ضغط: {last['systolic_pressure']}/{last['diastolic_pressure']}"
                    self.last_bp_label.configure(text=bp_text)
                
                # آخر قياس سكر
                if last['blood_sugar']:
                    sugar_text = f"آخر قياس سكر: {last['blood_sugar']} mg/dL"
                    self.last_sugar_label.configure(text=sugar_text)
            
        except Exception as e:
            logger.error(f"خطأ في تحديث الإحصائيات السريعة: {e}")
    
    def update_recent_alerts(self):
        """تحديث التنبيهات الأخيرة"""
        if not self.current_user_id:
            return
        
        try:
            alerts = self.db_manager.get_alerts(user_id=self.current_user_id, unread_only=True)
            
            self.alerts_listbox.delete(0, tk.END)
            
            for alert in alerts[:5]:  # أحدث 5 تنبيهات
                alert_text = f"{alert['alert_date']} - {alert['message']}"
                self.alerts_listbox.insert(tk.END, alert_text)
            
            if not alerts:
                self.alerts_listbox.insert(tk.END, "لا توجد تنبيهات جديدة")
                
        except Exception as e:
            logger.error(f"خطأ في تحديث التنبيهات: {e}")
    
    def update_measurements_table(self):
        """تحديث جدول القياسات"""
        if not self.current_user_id:
            return
        
        try:
            # مسح البيانات الحالية
            for item in self.measurements_tree.get_children():
                self.measurements_tree.delete(item)
            
            # جلب القياسات الأخيرة
            measurements = self.db_manager.get_measurements(
                user_id=self.current_user_id,
                limit=100
            )
            
            # إضافة البيانات إلى الجدول
            for measurement in measurements:
                values = (
                    measurement['measurement_date'],
                    measurement['measurement_time'],
                    measurement['systolic_pressure'] or '--',
                    measurement['diastolic_pressure'] or '--',
                    measurement['blood_sugar'] or '--',
                    'صائم' if measurement['sugar_type'] == 'fasting' else 'بعد الأكل',
                    measurement['notes'] or ''
                )
                self.measurements_tree.insert('', tk.END, values=values)
                
        except Exception as e:
            logger.error(f"خطأ في تحديث جدول القياسات: {e}")
    
    # طرق معالجة الأحداث
    def show_new_user_dialog(self):
        """عرض نافذة إضافة مستخدم جديد"""
        try:
            dialog = NewUserDialog(self.root, self.db_manager)
            user_id = dialog.show()

            if user_id:
                # تحديث قائمة المستخدمين
                self.users = self.db_manager.get_users()

                # اختيار المستخدم الجديد
                self.current_user_id = user_id
                self.update_user_info()

                # تحديث البيانات
                self.update_quick_stats()
                self.update_recent_alerts()
                self.update_measurements_table()

                self.update_status(f"تم إضافة مستخدم جديد بنجاح")

        except Exception as e:
            logger.error(f"خطأ في عرض نافذة المستخدم الجديد: {e}")
            messagebox.showerror("خطأ", f"فشل في عرض النافذة:\n{e}")

    def show_new_measurement_dialog(self):
        """عرض نافذة إضافة قياس جديد"""
        try:
            if not self.current_user_id:
                messagebox.showwarning("تحذير", "يجب اختيار مستخدم أولاً")
                return

            dialog = NewMeasurementDialog(self.root, self.db_manager, self.current_user_id)
            measurement_id = dialog.show()

            if measurement_id:
                # تحديث البيانات
                self.update_quick_stats()
                self.update_recent_alerts()
                self.update_measurements_table()

                self.update_status(f"تم إضافة قياس جديد بنجاح")

        except Exception as e:
            logger.error(f"خطأ في عرض نافذة القياس الجديد: {e}")
            messagebox.showerror("خطأ", f"فشل في عرض النافذة:\n{e}")
    
    def show_measurements_view(self):
        """عرض نافذة القياسات"""
        self.notebook.select(1)  # التبديل إلى تبويب القياسات
    
    def show_charts_view(self):
        """عرض نافذة الرسوم البيانية"""
        messagebox.showinfo("قريباً", "سيتم إضافة هذه الميزة قريباً")
    
    def show_reports_view(self):
        """عرض نافذة التقارير"""
        messagebox.showinfo("قريباً", "سيتم إضافة هذه الميزة قريباً")
    
    def show_settings_dialog(self):
        """عرض نافذة الإعدادات"""
        messagebox.showinfo("قريباً", "سيتم إضافة هذه الميزة قريباً")
